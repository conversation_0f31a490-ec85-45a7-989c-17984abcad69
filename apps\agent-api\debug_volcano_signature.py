#!/usr/bin/env python3
"""
调试火山引擎签名问题的脚本
"""
import asyncio
import json
import sys
import os
import hashlib
import hmac
import base64
from datetime import datetime, timezone
from urllib.parse import quote
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from api.services.volcano_client_service import VolcanoClientService
from api.settings import settings, logger

def manual_signature_test():
    """手动测试签名生成过程，对比官方文档示例"""
    print("🔍 手动测试火山引擎签名生成过程...")

    # 使用官方文档中的示例值进行测试
    access_key = settings.VOLCANO_ACCESS_KEY_ID
    secret_key_raw = settings.VOLCANO_SECRET_ACCESS_KEY

    # 解码Secret Key
    try:
        secret_key = base64.b64decode(secret_key_raw).decode('utf-8')
        print(f"✅ Secret Key解码成功: {secret_key[:10]}...")
    except Exception as e:
        print(f"❌ Secret Key解码失败: {e}")
        secret_key = secret_key_raw

    # 测试数据
    method = "POST"
    host = "rtc.volcengineapi.com"
    path = "/"
    query_params = {"Action": "StartVoiceChat", "Version": "2024-12-01"}
    body_data = {"AppId": "test", "RoomId": "test_room"}
    body = json.dumps(body_data, separators=(',', ':')).encode('utf-8')

    # 时间戳 - 使用固定时间便于调试
    t = datetime.now(timezone.utc)
    x_date = t.strftime('%Y%m%dT%H%M%SZ')
    date_stamp = t.strftime('%Y%m%d')

    print(f"📋 测试参数:")
    print(f"  - Method: {method}")
    print(f"  - Host: {host}")
    print(f"  - Path: {path}")
    print(f"  - Query: {query_params}")
    print(f"  - Body: {body.decode('utf-8')}")
    print(f"  - X-Date: {x_date}")
    print(f"  - Date Stamp: {date_stamp}")

    # 1. 创建规范请求
    canonical_uri = "/"
    canonical_querystring = '&'.join([f'{quote(str(k), safe="")}={quote(str(v), safe="")}' for k, v in sorted(query_params.items())])
    payload_hash = hashlib.sha256(body).hexdigest()

    # 根据火山引擎官方文档，只使用host和x-date头部
    canonical_headers = f'host:{host}\n'
    canonical_headers += f'x-date:{x_date}\n'
    signed_headers = 'host;x-date'

    canonical_request = "\n".join([
        method,
        canonical_uri,
        canonical_querystring,
        canonical_headers,
        "",  # 空行
        signed_headers,
        payload_hash
    ])

    print(f"\n📋 规范请求:")
    print(f"```\n{canonical_request}\n```")

    canonical_request_hash = hashlib.sha256(canonical_request.encode('utf-8')).hexdigest()
    print(f"📋 规范请求SHA256: {canonical_request_hash}")

    # 2. 创建待签名字符串
    algorithm = 'HMAC-SHA256'
    region = 'cn-north-1'
    service = 'rtc'
    credential_scope = f'{date_stamp}/{region}/{service}/request'
    string_to_sign = '\n'.join([
        algorithm,
        x_date,
        credential_scope,
        canonical_request_hash
    ])

    print(f"\n📋 待签名字符串:")
    print(f"```\n{string_to_sign}\n```")

    # 3. 生成签名密钥
    print(f"\n📋 生成签名密钥:")
    print(f"  - Secret Key: {secret_key[:10]}...")

    k_date = hmac.new(secret_key.encode('utf-8'), date_stamp.encode('utf-8'), hashlib.sha256).digest()
    print(f"  - kDate: {k_date.hex()}")

    k_region = hmac.new(k_date, region.encode('utf-8'), hashlib.sha256).digest()
    print(f"  - kRegion: {k_region.hex()}")

    k_service = hmac.new(k_region, service.encode('utf-8'), hashlib.sha256).digest()
    print(f"  - kService: {k_service.hex()}")

    k_signing = hmac.new(k_service, b'request', hashlib.sha256).digest()
    print(f"  - kSigning: {k_signing.hex()}")

    # 4. 计算最终签名
    signature = hmac.new(k_signing, string_to_sign.encode('utf-8'), hashlib.sha256).hexdigest()
    print(f"\n📋 最终签名: {signature}")

    # 5. 生成Authorization头
    authorization_header = f"{algorithm} Credential={access_key}/{credential_scope}, SignedHeaders={signed_headers}, Signature={signature}"
    print(f"\n📋 Authorization头:")
    print(f"```\n{authorization_header}\n```")

async def test_volcano_signature():
    """测试火山引擎签名"""
    print("\n" + "="*50)
    print("🔍 开始测试火山引擎API调用...")

    try:
        # 创建火山引擎客户端
        volcano_client = VolcanoClientService()
        print("✅ 火山引擎客户端创建成功")

        # 测试最简单的API调用 - 只包含必需参数
        test_data = {
            "AppId": settings.VOLCANO_RTC_APP_ID
        }

        print("🚀 开始调用火山引擎StartVoiceChat API...")

        # 调用API
        result = await volcano_client._call_volcano_api_with_retry(
            action="StartVoiceChat",
            data=test_data,
            operation_desc="测试签名"
        )

        print("✅ 火山引擎API调用成功!")
        print(f"📥 响应: {json.dumps(result, indent=2, ensure_ascii=False)}")

    except Exception as e:
        print(f"❌ 火山引擎API调用失败: {e}")
        print(f"📋 错误类型: {type(e).__name__}")

if __name__ == "__main__":
    manual_signature_test()
    asyncio.run(test_volcano_signature())
