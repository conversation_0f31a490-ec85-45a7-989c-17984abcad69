Title: Function Calling（非流式返回结果）--实时音视频-火山引擎

URL Source: https://www.volcengine.com/docs/6348/1359441

Markdown Content:
Function Calling（非流式返回结果）--实时音视频-火山引擎

===============

在实时对话式 AI场景下，通过使用 Function Calling 功能允许大模型识别用户对话中的特定需求，智能调用外部函数、API 等工具来执行它自身无法独立完成的任务，如处理实时数据检索、文件处理、数据库查询等。适用于天气查询、股票行情查询、数学计算等场景。

说明

该功能仅在使用火山方舟平台模型时生效。且只有在使用 doubao 非1.5 代系模型时，按照非流式返回 Function Calling 结果。

服务端和客户端均可实现该功能。你可根据业务请求端的类型选择对应的方式。例如你在开发 AI 应用时，选择服务端响应请求，建议使用服务端实现传入大模型上下文，降低请求延迟。

时序图
---

你可参看如下时序图在该场景下使用 Function Calling 功能：

![Image 6](https://lf3-static.bytednsdoc.com/obj/eden-cn/UJjvKJ%5BY/ljhwZthlaukjlkulzlp/1359441_plantuml_diagram0.png)

步骤 1：开启 Function Calling 功能。

 步骤 2：触发 Function Calling 后接收工具调用指令消息。

 步骤 3：执行本地工具获取工具调用结果，并将结果信息传回 RTC 服务端。

 步骤 4：收到音频回复。

 其中步骤 2 、3 支持多轮 Function calling 调用。当 Function Calling 的流程结束后，用户会收到房间内智能体的音频回复消息。

服务端实现
-----

### 步骤 1：开启 Function Calling 功能

你需要调用 [startVoiceChat](https://www.volcengine.com/docs/6348/1558163) 接口配置以下字段开启该功能：

1.   通过 `LLMConfig.Tools`字段配置一组或多组 Function（函数）工具相关的功能和定义。
2.   通过`LLMConfig.FunctionCallingConfig`字段配置接收 Function Calling 功能返回的消息的 URL 和鉴权签名，返回消息包括函数被调用时触发的通知消息和函数调用指令消息。 
    1.   `ServerMessageUrl`：接收 Function Calling 功能返回消息的 URL 地址。你指定的 URL 地址将收到来自 RTC 服务器的 HTTP(S) POST 请求发送的指令消息，格式为 JSON。
    2.   `ServerMessageSignature`：鉴权签名。你可传入该鉴权字段，在收到 Function Calling 功能返回消息时，与步骤 2 返回工具调用指令消息中的 `signature` 字段的值进行对比，用于鉴权，保证消息的可靠性与安全性。

你可以参考以下示例代码进行请求：

```JSON
POST https: //rtc.volcengineapi.com?Action=StartVoiceChat&Version=2024-12-01
{
    "AppId": "661e****543cf",
    "RoomId": "Room1",
    "TaskId": "task1",
    "Config": {
        "ASRConfig": {
            "Provider": "volcano",
            "ProviderParams": {
                "Mode": "bigmodel",
                "AppId": "93****21",
                "AccessToken": "MOaOa*****HA4h5B",
                "ApiResourceId": "volc.bigasr.sauc.duration",
                "StreamMode": 0
            }
        },
        "TTSConfig": {
            "Provider": "volcano_bidirection",
            "ProviderParams": {
                "app": {
                    "appid": "94****11",
                    "token": "OaO****ws1"
                },
                "audio": {
                    "voice_type": "zh_male_qingshuangnanda_mars_bigtts",
                    "speech_rate": 0,
                    "pitch_rate": 0
                },
                "ResourceId": "volc.service_type.10029"
            }
        },
        "LLMConfig": {
            "Mode": "ArkV3",
            "EndPointId": "epid****212",
            "MaxTokens": 1024,
            "Temperature": 0.1,
            "TopP": 0.3,
            "SystemMessages": [
                "你是小宁，性格幽默又善解人意。你在表达时需简明扼要，有自己的观点。"
            ],
            "UserPrompts": [
                {
                    "Role": "user",
                    "Content": "你好"
                },
                {
                    "Role": "assistant",
                    "Content": "有什么可以帮到你的？"
                }
            ],
            "HistoryLength": 3,
            "Tools": [
                {
                    "Type": "function",
                    "function": {
                        "name": "get_current_weather",
                        "description": "获取给定地点的天气",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "location": {
                                    "type": "string",
                                    "description": "地理位置，比如北京市"
                                },
                                "unit": {
                                    "type": "string",
                                    "description": "",
                                    "enum": [
                                        "摄氏度",
                                        "华氏度"
                                    ]
                                }
                            },
                            "required": [
                                "location"
                            ]
                        }
                    }
                }
            ]
        }
        "FunctionCallingConfig": {
            "ServerMessageUrl": "https://example-domain.com/vertc/fc",
            "ServerMessageSignature": "b46a****8ad6a",
        },
        "AgentConfig": {
            "TargetUserId": [
                "user1"
            ],
            "WelcomeMessage": "Hello",
            "UserId": "BotName001"
        }
    }
}
```

JSON

### 步骤 2：接收工具调用指令消息

当用户的问题触发 Function Calling 时，会通过你在步骤 1 配置的 URL 地址，使用 HTTP(S) 请求返回本次函数工具调用的指令消息，返回的格式为 JSON 格式，内容如下：

| 字段名 | 类型 | 描述 |
| --- | --- | --- |
| message | Array of [message](https://www.volcengine.com/docs/6348/1359441#message2) | 调用指令消息详情。 |
| signature | String | `StartVoiceChat.Config.FunctionCallingConfig` 中设置的 `signature`的值，用于鉴权。 |

message：

| 字段名 | 类型 | 描述 |
| --- | --- | --- |
| id | String | 本次 Function Calling 任务的标识 ID。 |
| type | String | Function Calling 调用工具类型，固定为 `function`，表示为函数调用。 |
| function | [function](https://www.volcengine.com/docs/6348/1359441#function) | 调用函数详情。 |

function：

| 字段名 | 类型 | 描述 |
| --- | --- | --- |
| name | String | 函数名称。 |
| arguments | String | 函数调用指令详情。 |

### 步骤 3：将工具调用的结果信息传回 RTC 服务端

在获得工具调用指令消息后，你需要调用本地工具获取对应的结果。在获得结果后，你需要将其传回 RTC 服务端，经 LLM 和 TTS 模块处理后进行播放。

 你可调用 [UpdateVoiceChat](https://www.volcengine.com/docs/6348/1316245) 接口设置以下参数将工具调用的结果信息传回 RTC 服务端：

*   `AppId`：RTC 应用的 AppID。
*   `RoomId`：智能体与真人进行通话的房间的 ID。
*   `TaskId`：智能体任务 ID。
*   `Command`：固定为 `function`，表示传入工具调用的结果信息。
*   `Message`：工具调用的结果信息，需为 JSON 转义字符串。例如：`{\"ToolCallID\":\"call_cx\",\"Content\":\"上海天气是台风\"}`。

你可参看以下示例将工具调用的结果信息传回 RTC 服务端：

```JSON
POST https://rtc.volcengineapi.com?Action=UpdateVoiceChat&Version=2024-06-01
{
    "AppId": "661e****543cf",
    "RoomId": "Room1",
    "TaskId": "task1",
    "Command": "function",
    "Message":"{\"ToolCallID\":\"call_cx\",\"Content\":\"上海天气是台风\"}"
}
```

JSON

### 步骤 4：获取 Function Calling 最终答复

当 Function Calling 的流程结束后，用户会收到房间内智能体的音频回复消息。

客户端实现
-----

### 步骤 1：开启 Function Calling 功能

你需要调用 [startVoiceChat](https://www.volcengine.com/docs/6348/1558163) 接口配置 `LLMConfig.Tools` 字段，输入你需要的一组或多组 Function（函数）工具相关的功能和定义开启该功能。

 你可以参考以下示例代码进行请求：

```JSON
POST https://rtc.volcengineapi.com?Action=StartVoiceChat&Version=2024-12-01
{
    "AppId": "661e****543cf",
    "RoomId": "Room1",
    "TaskId": "task1",
    "Config": {
        "ASRConfig": {
            "Provider": "volcano",
            "ProviderParams": {
                "Mode": "bigmodel",
                "AppId": "93****21",
                "AccessToken": "MOaOa*****HA4h5B",
                "ApiResourceId": "volc.bigasr.sauc.duration",
                "StreamMode": 0
            }
        },
        "TTSConfig": {
            "Provider": "volcano_bidirection",
            "ProviderParams": {
                "app": {
                    "appid": "94****11",
                    "token": "OaO****ws1"
                },
                "audio": {
                    "voice_type": "zh_male_qingshuangnanda_mars_bigtts",
                    "speech_rate": 0,
                    "pitch_rate": 0
                },
                "ResourceId": "volc.service_type.10029"
            }
        },
        "LLMConfig": {
            "Mode": "ArkV3",
            "EndPointId": "epid****212",
            "MaxTokens": 1024,
            "Temperature": 0.1,
            "TopP": 0.3,
            "SystemMessages": [
                "你是小宁，性格幽默又善解人意。你在表达时需简明扼要，有自己的观点。"
            ],
            "UserMessages": [
                "user:\"你是谁\"",
                "assistant:\"我是问答助手\"",
                "user:\"你能干什么\"",
                "assistant:\"我能回答问题\""
            ],
            "HistoryLength": 3,
            "Tools": [
                {
                    "Type": "function",
                    "function": {
                        "name": "get_current_weather",
                        "description": "获取给定地点的天气",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "location": {
                                    "type": "string",
                                    "description": "地理位置，比如北京市"
                                },
                                "unit": {
                                    "type": "string",
                                    "description": "",
                                    "enum": [
                                        "摄氏度",
                                        "华氏度"
                                    ]
                                }
                            },
                            "required": [
                                "location"
                            ]
                        }
                    }
                }
            ]
        }
    },
    "AgentConfig": {
        "TargetUserId": [
            "user1"
        ],
        "WelcomeMessage": "Hello",
        "UserId": "BotName001"
    }
}
```

JSON

### 步骤 2：接收工具调用指令消息

当用户的问题触发 Function Calling 时，你可通过 [onRoomBinaryMessageReceived](https://www.volcengine.com/docs/6348/70081#IRTCRoomEventHandler-onroombinarymessagereceived) 回调接收本次函数工具调用的指令消息。该回调中的 message 字段中的内容为函数调用指令消息，格式为二进制，使用前需解析。

 message 的格式如下：

![Image 7: Image](https://portal.volccdn.com/obj/volcfe/cloud-universal-doc/upload_e443d32835b3e8deec00aa67da71e64f.jpg)

| 参数名 | 类型 | 描述 |
| --- | --- | --- |
| magic number | String | 消息格式，固定为 `tool`，表示此次消息为函数工具指令消息。 |
| length | String | 信息指令长度，单位为 bytes。存放方式为大端序。 |
| Tool_Calls | String | 信息指令详细信息。格式参看[Tool_Calls](https://www.volcengine.com/docs/6348/1359441#toolcalls)。 |

Tool_Calls

| 参数名 | 类型 | 是否必填 | 描述 |
| --- | --- | --- | --- |
| tool_calls | Array of [tool_call](https://www.volcengine.com/docs/6348/1359441#toolcall) | 是 | 工具调用信息列表。 |
| id | String | 是 | 本次工具调用的唯一标识 ID。 |
| type | String | 是 | 工具类型。 |

tool_call

| 字段名 | 类型 | 描述 |
| --- | --- | --- |
| function | [function](https://www.volcengine.com/docs/6348/1359441#function2) | 调用函数详情。 |

function

| 字段名 | 类型 | 描述 |
| --- | --- | --- |
| name | String | 函数名称。 |
| arguments | String | 函数调用指令详情。 |

你可参看以下示例代码对工具调用指令消息进行解析。

```C++
//定义结构体
struct function {
    std::string arguments;
    std::string name;
};

struct ToolCall {
    std::string id;
    std::string type;
    function func;
};

struct ToolCallsMsgData {
    std::string subscribe_user_id;
    std::vector<ToolCall> tool_calls
};

//回调事件
void onRoomBinaryMessageReceived(const char* uid, int size, const uint8_t* message) {
    std::string tool_calls;
    bool ret = Unpack(message, size, tool_calls);
    if(ret) {
        ParseData(tool_calls);
    }
}

//拆包校验
bool Unpack(const uint8_t *message, int size, std::string& tool_calls_msg) {
    int kToolCallsHeaderSize = 8;
    if(size < kToolCallsHeaderSize) { 
        return false;
    }
    // magic number "tool"
    if(static_cast<uint32_t>((static_cast<uint32_t>(message[0]) << 24) 
           | (static_cast<uint32_t>(message[1]) << 16) 
           | (static_cast<uint32_t>(message[2]) << 8) 
           | static_cast<uint32_t>(message[3])) != 0x746F6F6CU) {
        return false;
    }
    
    uint32_t length = static_cast<uint32_t>((static_cast<uint32_t>(message[4]) << 24) 
           | (static_cast<uint32_t>(message[5]) << 16) 
           | (static_cast<uint32_t>(message[6]) << 8) 
           | static_cast<uint32_t>(message[7]));
           
    if(size - kToolCallsHeaderSize != length) {
        return false;
    }

    if(length) {
        tool_calls_msg.assign((char*)message + kToolCallsHeaderSize, length);
    } else {
        tool_calls_msg = "";
    }
    return true;
}

//解析
void ParseData(const std::string& msg) {
    // 解析 JSON 字符串
    nlohmann::json json_data = nlohmann::json::parse(msg);
    ToolCallsMsgData toolcalls_data;
    // 存储解析后的数据
    toolcalls_data.subscribe_user_id = json_data["subscribe_user_id"];
    // 遍历 JSON 数据并填充结构体
    for (const auto& item : json_data["tool_calls"]) {
        ToolCall tool_call;
        tool_call.id = item["id"];
        tool_call.type = item["type"];
        auto fun_json = item["function"];
        tool_call.func.arguments = fun_json["arguments"];
        tool_call.func.name = fun_json["name"];
        toolcalls_data.push_back(tool_call);
    }
}
```

C++

### 步骤 3：将工具调用的结果信息传回 RTC 服务端

在获得工具调用指令消息后，你需要调用本地工具获取对应的结果。在获得结果后，你需要将其传回 RTC 服务端，经 LLM 和 TTS 模块处理后进行播放。

 你可调用[SendUserBinaryMessage](https://www.volcengine.com/docs/6348/70080#RTCRoom-senduserbinarymessage) 接口设置以下参数将函数调用指令消息按照二进制格式传回 RTC 服务端：

*   `userId`：消息接收用户的 ID
*   `buffer`：工具调用的结果信息。
*   `config`：发送消息的可靠有序性。

指令消息格式如下：

![Image 8: Image](https://portal.volccdn.com/obj/volcfe/cloud-universal-doc/upload_da013d2aabd3d8c6bdcf225963c927bf.jpg)

| 参数名 | 类型 | 描述 |
| --- | --- | --- |
| magic number | String | 消息格式，固定为 `func`。 |
| length | String | 工具调用的结果信息长度，单位为 bytes。存放方式为大端序。 |
| Function_Response | String | 工具调用的结果信息。 |

你可参看以下示例代码传回工具调用的结果信息。

TypeScript

Java

```TypeScript
import VERTC from '@volcengine/rtc';

/**
 * @brief 将字符串包装成 TLV
 */
function stringToTLV(inputString: string) {
  const type = 'func';
  const typeBuffer = new Uint8Array(4);

  for (let i = 0; i < type.length; i++) {
    typeBuffer[i] = type.charCodeAt(i);
  }

  const lengthBuffer = new Uint32Array(1);
  const valueBuffer = new TextEncoder().encode(inputString);

  lengthBuffer[0] = valueBuffer.length;

  const tlvBuffer = new Uint8Array(typeBuffer.length + 4 + valueBuffer.length);

  tlvBuffer.set(typeBuffer, 0);

  tlvBuffer[4] = (lengthBuffer[0] >> 24) & 0xff;
  tlvBuffer[5] = (lengthBuffer[0] >> 16) & 0xff;
  tlvBuffer[6] = (lengthBuffer[0] >> 8) & 0xff;
  tlvBuffer[7] = lengthBuffer[0] & 0xff;

  tlvBuffer.set(valueBuffer, 8);

  return tlvBuffer.buffer;
};

/**
 * @brief TLV 数据格式转换成字符串
 * @note TLV 数据格式
 * | magic number | length(big-endian) | value |
 * @param {ArrayBufferLike} tlvBuffer
 * @returns 
 */
function tlv2String(tlvBuffer: ArrayBufferLike) {
  const typeBuffer = new Uint8Array(tlvBuffer, 0, 4);
  const lengthBuffer = new Uint8Array(tlvBuffer, 4, 4);
  const valueBuffer = new Uint8Array(tlvBuffer, 8);

  let type = '';
  for (let i = 0; i < typeBuffer.length; i++) {
    type += String.fromCharCode(typeBuffer[i]);
  }

  const length =
    (lengthBuffer[0] << 24) | (lengthBuffer[1] << 16) | (lengthBuffer[2] << 8) | lengthBuffer[3];

  const value = new TextDecoder().decode(valueBuffer.subarray(0, length));

  return { type, value };
};

/**
 * @brief 通过 onRoomBinaryMessageReceived 接收 toolcall
 *        通过 sendUserBinaryMessage 发送 response
 */
function handleRoomBinaryMessageReceived(
  event: {
    userId: string;
    message: ArrayBuffer;
  },
) {
  const { message } = event;
  const { type, value } = tlv2String(message);
  const data = JSON.parse(value);
  const { tool_calls } = data || {};
  // 处理逻辑
  console.log(type);
  
  if (tool_calls?.length) {
    const name: string = tool_calls?.[0]?.function?.name;
    const map: Record<string, string> = {
      getcurrentweather: '今天下雪， 最低气温零下10度',
      musicplayer: '查询到李四的歌曲， 名称是千里之内',
      sendmessage: '发送成功',
    };
    this.engine.sendUserBinaryMessage(
      'Your AI Bot Name',
      stringToTLV(
        JSON.stringify({
          ToolCallID: tool_calls?.[0]?.id,
          Content: map[name.toLocaleLowerCase().replaceAll('_', '')],
        })
      )
    );
  }
};

/**
 * @brief 监听房间内二进制消息
 */
this.engine.on(VERTC.events.onRoomBinaryMessageReceived, handleRoomBinaryMessageReceived);
```

TypeScript

```Java
public void SendFunctionResponse(String ToolCallID, String Content) {
    JSONObject json = new JSONObject();
    try {
        json.put("ToolCallID", ToolCallID);
        json.put("Content", Content);
    } catch (JSONException e) {
        throw new RuntimeException(e);
    }
    String jsonString = json.toString();
    System.out.println(jsonString);
    app.rtcSdkWrap.sendUserBinaryMessage("RobotMan_", stringToTLV(jsonString));
}
public byte[] stringToTLV(String content) {
    String func_type = "func";
    byte[] prefixBytes = func_type.getBytes(StandardCharsets.UTF_8);
    byte[] contentBytes = content.getBytes(StandardCharsets.UTF_8);
    int contentLength = contentBytes.length;
    ByteBuffer buffer = ByteBuffer.allocate(prefixBytes.length + 4 + contentLength);
    buffer.order(ByteOrder.BIG_ENDIAN);
    buffer.put(prefixBytes);
    buffer.putInt(contentLength);
    buffer.put(contentBytes);
    return buffer.array();
}
```

Java

### 步骤 4：获取 Function Calling 最终答复

当 Function Calling 的流程结束后，用户会收到房间内智能体的音频回复消息。

FAQ
---

Q1：配置了 Function Calling 后，通过 updateVoiceChat/sendUserBinaryMessage 返回给智能体的工具调用结果信息可以不读出来吗?

 A1：不可以。

Q2：配置了 Function Calling 后，相关配置如何更新？

 A1：需要先调用 `StopVoiceChat` 接口停止智能体任务，然后再调用 `StartVoiceChat` 接口重新配置 Function Calling 功能。
