Title: 请求结构--实时音视频-火山引擎

URL Source: https://www.volcengine.com/docs/6348/69828

Markdown Content:
请求结构--实时音视频-火山引擎



您可使用 OpenAPI 向 RTC 的服务端地址发送请求，并按照接口说明在请求中加入相应请求参数。系统会根据请求的处理情况，返回处理结果。本章节为您介绍 RTC OpenAPI 的调用方法，包括但不限于请求结构、公共参数、签名机制和公共错误码

### 服务接入地址

RTC 在全球多个地域部署，请您参考下表来使用对应的接入地址。如果接口不支持该表中的所有地域，则会在接口文档中单独说明。

| 地域名称 | Region | 接入地址 |
| --- | --- | --- |
| 华北 | cn-north-1 | rtc.volcengineapi.com |
| 亚太东南（柔佛） | ap-southeast-1 | open-ap-singapore-1.volcengineapi.com |

### 通信协议

你可以使用 HTTP 和 HTTPS 两种协议进行请求通信。我们强烈推荐你使用安全性更高的 HTTPS 方式发送请求。

### 字符编码

请求及返回结果使用 UTF-8 字符集进行编码。

### 接口限制

具体值请参看各个接口描述。

当前以火山引擎账号维度进行限制, 账号下多个 AppId 之间共享限流额度。

### 请求方法

根据各个接口的具体需求，选择 Get 或 Post 方式发起请求。

### 请求参数

公共请求参数参看公共参数。

各接口特有请求参数参看各接口描述。

对于 ID 类请求参数（BusinessId，TaskId，RoomId 和 UserId），必须遵循统一的 ID 命名规范：

 字符串长度不超过 128 字符；

 字符串中仅包含以下的字符：

 a~z （小写英文字符）

 A~Z （大写英文字符）

 0~9 （数字）

 @ . _ -

### 构造 URI

请求 URI 的组成结构：`{URI-scheme}://{Endpoint}/?{Query-string}`。

![Image 4: alt](https://portal.volccdn.com/obj/volcfe/cloud-universal-doc/upload_4f4f5be089eb362e5a889a5e778b793b.png)

参数说明如下表所示。

| 参数 | 描述 |
| --- | --- |
| URI-scheme | 表示用于传输请求的协议，支持通过**HTTP**和**HTTPS**2 种方式进行请求通信。 |
| Endpoint | API 的服务接入地址。 * 中国大陆：`rtc.volcengineapi.com`。 * 其他国家/地区：`open-ap-singapore-1.volcengineapi.com` |
| Query-string | 查询字符串，包含**公共参数**和 GET 请求中的**查询参数**。* **公共参数**：需要包含 Action 和 Version 参数；参数前面需要带一个“**?**”，公共参数之间用“**&**”相连。 * **查询参数**(GET)：从具体的 OpenAPI 接口文档中获取；查询参数前面需要带一个“**?**”，形式为“参数名=参数取值”，参数之间用“**&**”相连。例如“?limit=5”，表示查询不超过 5 条数据。 |

**请求示例**

```
POST https://rtc.volcengineapi.com?Action=BanRoomUser&Version=2020-12-01

{
    "AppId": "Your_AppId",
    "RoomId": "Your_RoomId",
    "UserId": "Your_UserId",
    "ForbiddenInterval": 0
}
```

**返回结果**

```
{
    "ResponseMetadata": {
        "RequestId": "Your_RequestId",
        "Action": "BanRoomUser",
        "Version": "2020-12-01",
        "Service": "rtc",
        "Region": "cn-north-1"
    },
    "Result": {
        "message": "success"
    }
}
```

