Title: 公共参数--实时音视频-火山引擎

URL Source: https://www.volcengine.com/docs/6348/1178321

Markdown Content:
公共参数--实时音视频-火山引擎

===============
公共参数是每个接口都需要使用的请求参数，开发者每次使用火山引擎 API 发送请求时都需要携带这些公共请求参数，否则会导致请求失败。

### 请求参数

在发起请求时，需要包含两类参数：公共请求参数和接口特有的请求参数。

#### 公共请求参数

在发起所有 OpenAPI 请求时，请求中必须包含以下公共参数：

| **名称** | **类型** | **取值** | **位置** | **说明** |
| --- | --- | --- | --- | --- |
| Host | string | `rtc.volcengineapi.com` | header | 同[服务地址](https://www.volcengine.com/docs/6348/69828#address)。 |
| Content-Type | string | / | header | 资源的 MIME 类型。 * Post 请求中，该值为 `application/json`。 * Get 请求中,该值可为空。 |
| X-Date | string | / | header | 请求时间，UTC 时间，形如`20201230T081805Z` 。 |
| X-Content-Sha256 | string | / | header | 对请求体采用 SHA256 加密后的结果字符串。 |
| Authorization | string | / | header | 签名。关于如何获得这一签名，参看[对 OpenAPI 请求进行签名](https://www.volcengine.com/docs/6348/69859)。 |
| Action | string | / | Query String | 接口名称。参看接口详情文档。 |
| Version | string | / | Query String | 接口版本。参看接口详情文档。 |

对于接口特有请求参数，参看具体 OpenAPI 接口文档。
