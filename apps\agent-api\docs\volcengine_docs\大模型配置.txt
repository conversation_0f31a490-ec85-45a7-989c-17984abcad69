Title: 大模型配置--实时音视频-火山引擎

URL Source: https://www.volcengine.com/docs/6348/1581714

Markdown Content:
大模型配置--实时音视频-火山引擎


在实时对话式 AI 场景中，你需要配置大模型（LLM）能力，解析用户输入，生成智能回复，管理上下文等。RTC 提供一站式接入方案，只需在 `StartVoiceChat` 接口配置 `LLMConfig` 即可完成大模型接入。本文将详细介绍不同平台的配置参数、注意事项及适用场景，帮助你根据需求选择合适的大模型方案。

选择适合的大模型平台
----------

火山引擎提供三种大模型接入平台，以满足不同开发场景的需求：

*   **火山方舟平台**：火山引擎提供的大模型服务平台，适用于需要调用火山引擎官方基础模型（如 Doubao、DeepSeek 等）或使用应用实验室功能（零代码组合插件）的场景，支持视觉理解、Function Calling 等高级能力。
*   **Coze 平台**：适用于快速搭建零代码/低代码 AI 应用的场景。
*   **第三方大模型**：适用于需要集成自有大模型或特定第三方服务（如开源大模型、垂直领域专用模型）的定制化场景。

火山方舟平台
------

注意

在实时对话式 AI 场景，为避免智能体回复耗时过长，保证对话的流畅性，建议使用非深度思考大模型。

### 核心参数

使用火山方舟平台时，`StartVoiceChat.LLMConfig` 结构核心配置参数如下：

> 完整参数及说明可参看 [StartVoiceChat](https://www.volcengine.com/docs/6348/1558163#yh0bMioaxx) 接口。

| 参数名 | 类型 | 是否必填 | 示例值 | 说明 |
| --- | --- | --- | --- | --- |
| `Mode` | String | 是 | `ArkV3` | 大模型平台标识。使用火山方舟平台时，该参数固定取值：`ArkV3`。 |
| `EndPointId` | String | 选填（与 `BotId` 二选一） | `ep-22****212` | 自定义推理接入点 ID（用于调用基础模型推理功能）。 |
| `BotId` | String | 选填（与 `EndPointId` 二选一） | `botid****212` | 应用 ID（用于使用方舟应用实验室功能）。 |
| `Temperature` | Float | 选填 | `0.1` | 采样温度，用于控制生成文本的随机性和创造性，值越大随机性越高。取值范围为 `（0,1]`，默认值为 `0.1`。 |
| `MaxTokens` | Integer | 选填 | `1024` | 输出文本的最大 token 限制。默认值为 `1024`。 |
| `TopP` | Float | 选填 | `0.3` | 采样选择范围。控制输出 token 多样性，值越大类型越丰富。取值范围为`[0,1]`，默认值为 `0.3`。 |
| `SystemMessages` | String[] | 选填 | `["你是小宁，性格幽默又善解人意。"]` | 系统提示词。用于输入控制大模型行为方式的指令，定义了模型的角色、行为准则，特定的输出格式等。 |
| `UserPrompts` | Object[] | 选填 | `[{"Role": "user", "Content": "你好"}, {"Role": "assistant", "Content": "有什么可以帮到你？"}]` | 用户提示词，可用于增强模型的回复质量，模型回复时会优先参考此处内容，引导模型生成特定的输出或执行特定的任务。 |
| `HistoryLength` | Integer | 选填 | `3` | 历史问题轮数，控制上下文存储长度。默认值为 `3`。 |
| `Tools` | Object[] | 选填 | - | 使用 Function calling 功能时，模型可以调用的工具列表。仅支持调用函数。该功能使用方法参看[Function Calling 功能说明文档](https://www.volcengine.com/docs/6348/1554654)。 |
| `Prefill` | Boolean | 选填 | `false` | 是否将 ASR 中间结果提前送入大模型，以降低延时，但会增加模型消耗。 |
| `VisionConfig` | Object | 选填 | - | 视觉理解能力配置，仅 Doubao vision 系列模型生效。该功能使用方法参看[视觉理解能力说明文档](https://www.volcengine.com/docs/6348/1408245) |
| `ThinkingType` | String | 选填 | `disabled` | 设置大模型的深度思考模式。若你使用的是深度思考大模型，强烈建议设置该字段为 disabled 以关闭深度思考模式，避免智能体回复耗时过长，影响对话的流畅性。 |

### 请求示例

以通过自定义推理接入点，接入火山方舟平台的大模型为例，示例如下：

```json
{
    "LLMConfig": {
        "Mode": "ArkV3",
        "EndPointId": "epid****212",
        "MaxTokens": 1024,
        "Temperature": 0.1,
        "TopP": 0.3,
        "SystemMessages": [
            "你是小宁，性格幽默又善解人意。你在表达时需简明扼要，有自己的观点。"
        ],
        "UserPrompts": [
            {
                "Role": "user",
                "Content": "你好"
            },
            {
                "Role": "assistant",
                "Content": "有什么可以帮到你的？"
            }
        ],
        "HistoryLength": 3,
        "Tools": [
            {
                "Type": "function",
                "function": {
                    "name": "get_current_weather",
                    "description": "获取给定地点的天气",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "location": {
                                "type": "string",
                                "description": "地理位置，比如北京市"
                            },
                            "unit": {
                                "type": "string",
                                "description": "",
                                "enum": [
                                    "摄氏度",
                                    "华氏度"
                                ]
                            }
                        },
                        "required": [
                            "location"
                        ]
                    }
                }
            }
        ]
    }
            "FunctionCallingConfig": {
                "ServerMessageUrl": "https://example-domain.com/vertc/fc",
                "ServerMessageSignature": "b46a****8ad6a",
    }
}
```

json

### 注意事项

#### EndPointId 与 BotId 区别

`EndPointId` 与 `BotId` 使用场景不同：

*   `EndPointId`：在需要使用模型推理功能（如直接调用部署的基础模型）时填入，且仅支持自定义推理接入点，不支持预置推理接入点。
*   `BotId`：在需要使用应用实验室功能（如零代码组合插件调用大模型）时填入，且创建应用时仅支持使用自定义推理接入点，不支持预置推理接入点。

#### 对话上下文管理

使用火山方舟平台时，模型对话上下文生成逻辑由系统提示词（SystemMessages）、用户提示词（UserPrompts 和 UserMessages）和

 历史问题轮数（HistoryLength）共同控制。具体参看[火山方舟上下文管理机制](https://www.volcengine.com/docs/6348/1581711#volc)。

#### Function Calling 功能

使用 Function Calling 功能允许大模型识别用户对话中的特定需求，智能调用外部函数、API 等工具来执行它自身无法独立完成的任务，如处理实时数据检索、文件处理、数据库查询等。该功能不支持与联网插件、知识库插件同时开启。该功能详细说明参看[Function Calling 功能说明文档](https://www.volcengine.com/docs/6348/1554654)。

 推荐使用 doubao-1.5 代系模型或 DeepSeek 模型，可更快收到返回结果。

#### 视觉理解能力

仅在使用 `EndPointId` 调用 Doubao 大模型时支持使用视觉理解能力，且创建自定义推理接入点时需选择 `vision` 系列模型，如 `Doubao-vision-pro-32k`。该功能详细说明参看[视觉理解能力说明文档](https://www.volcengine.com/docs/6348/1408245)。

Coze 平台
-------

### 核心参数

使用 Coze 平台时，`StartVoiceChat.LLMConfig` 结构核心配置参数如下：

> 完整参数及说明可参看 [StartVoiceChat](https://www.volcengine.com/docs/6348/1558163) 接口。

| 参数名 | 类型 | 是否必填 | 示例值 | 说明 |
| --- | --- | --- | --- | --- |
| `Mode` | String | 是 | `CozeBot` | 大模型平台标识。固定取值 `CozeBot`，标识使用 Coze 平台。 |
| `CozeBotConfig.Url` | String | 是 | `https://api.coze.cn` | 固定请求地址。该参数固定取值：`https://api.coze.cn`。 |
| `CozeBotConfig.BotId` | String | 是 | `73****68` | Coze 智能体 ID（从智能体开发页面获取）。 |
| `CozeBotConfig.APIKey` | String | 是 | `czu_UEE2mJn6****MHxLCVv9uQ7H` | Coze 访问密钥（需授权对应权限）。 |
| `CozeBotConfig.UserId` | String | 是 | `123` | 用户标识（用于隔离对话上下文）。 |
| `CozeBotConfig.HistoryLength` | Integer | 选填 | `3` | 历史问题轮数（控制上下文存储长度）。 |
| `CozeBotConfig.Prefill` | Boolean | 选填 | `false` | 是否将 ASR 中间结果提前送入大模型（降低延时但增加消耗）。 |
| `CozeBotConfig.EnableConversation` | Boolean | 选填 | `false` | 是否使用 Coze 平台上下文管理（开启后 `HistoryLength` 无效）。 |

### 请求示例

你可参看以下示例，使用Coze 平台进行大模型调用：

```json
{
    "LLMConfig": {
        "Mode": "CozeBot",
        "CozeBotConfig": {
            "Url": "https://api.coze.cn",
            "BotId": "73****68",
            "APIKey": "czu_UEE2mJn6****MHxLCVv9uQ7H",
            "UserId": "123",
            "HistoryLength": 3,
            "Prefill": false,
            "EnableConversation": false
        }
    }
}
```

json

### 注意事项

#### 智能体发布

在使用 `BotId` 调用 Coze 智能体前，该智能体必须发布为 API。发布操作可参看[发布智能体为 API 服务](https://www.coze.cn/open/docs/guides/publish_agent_api)。

#### 访问密钥

你可以生成[个人访问令牌](https://www.coze.cn/open/oauth/pats)以供测试。线上环境注意替换为 OAuth 访问密钥。你可根据不同的使用场景，选择不同的 OAuth 授权方式，详情参考 [OAuth 应用管理](https://www.coze.cn/open/docs/developer_guides/oauth_apps)。

说明

创建个人访问令牌或 OAuth 应用时，你需要根据你的 Bot 使用场景勾选对应权限，否则会鉴权失败。

#### 上下文管理

你可选择将上下文管理交由 Coze 平台管理，或由 RTC 管理。

*   RTC 管理：

`EnableConversation` 为 `false` 时，上下文由 RTC 管理，默认配置。
*   Coze 平台管理：

`EnableConversation` 为 `true` 时，上下文由 Coze 管理。此时你可以使用 Coze 平台上下文管理相关功能，如将指定内容添加到会话中。

不同平台管理上下文时控制参数不同，具体参看 [Coze 平台上下文管理机制](https://www.volcengine.com/docs/6348/1581711#coze)。

#### 能力限制

以下 Coze 能力， RTC 对话式 AI 场景下暂不支持：

*   视觉理解能力
*   Function Calling 功能
*   端插件能力
*   动态变量能力

如需使用以上能力，可使用 [Coze 服务端 + RTC SDK 方案](https://www.coze.cn/open/docs/developer_guides/create_room)。

#### 降低请求时延

在输入请求地址时，可使用 `https://bot-open-api.bytedance.net`内网域名，时延更小更稳定。

第三方大模型/Agent
------------

说明

集成前，需要你提供第三方大模型或 Agent 的服务请求接口，并确保该接口符合火山引擎 RTC 标准规范，否则需要对其进行改造。具体可参看[接入第三方大模型或 Agent](https://www.volcengine.com/docs/6348/1399966)。

### 核心参数

使用第三方大模型时，`StartVoiceChat.LLMConfig` 结构核心配置参数如下：

> 完整参数及说明可参看 [StartVoiceChat](https://www.volcengine.com/docs/6348/1558163) 接口。

| 参数名 | 类型 | 是否必填 | 示例值 | 说明 |
| --- | --- | --- | --- | --- |
| `Mode` | String | 是 | `CustomLLM` | 大模型平台名称。使用第三方大模型时，该参数固定取值： `CustomLLM`。 |
| `URL` | String | 是 | `https://api.***.com/v1/chat/completions` | 第三方大模型请求地址（需 HTTPS 域名）。 |
| `ModelName` | String | 选填 | `name1` | 第三方大模型名称。 |
| `APIKey` | String | 选填 | `pat*****123231` | 鉴权 Token（Bearer Token 认证方式）。 |
| `Temperature` | Float | 选填 | `0.1` | 采样温度，用于控制生成文本的随机性和创造性，值越大随机性越高。取值范围为 `（0,1]`，默认值为 `0.1`。 |
| `MaxTokens` | Integer | 选填 | `1024` | 输出文本的最大 token 限制。默认值为 `1024` |
| `TopP` | Float | 选填 | `0.3` | 采样选择范围。控制输出 token 多样性，值越大类型越丰富。取值范围为`[0,1]`，默认值为 `0.3`。 |
| `SystemMessages` | String[] | 选填 | `["你是小宁，性格幽默又善解人意。"]` | 系统提示词（定义模型角色、行为准则）。 |
| `UserPrompts` | Object[] | 选填 | `[{"Role": "user", "Content": "你好"}, {"Role": "assistant", "Content": "有什么可以帮到你？"}]` | 用户提示词，可用于增强模型的回复质量，模型回复时会优先参考此处内容，引导模型生成特定的输出或执行特定的任务。 |
| `HistoryLength` | Integer | 选填 | `3` | 历史问题轮数，控制上下文存储长度。默认值为 `3`。 |
| `Feature` | String | 选填 | `{"Http":true}` | 测试标记（如 `{"Http":true}` 允许 HTTP 域名测试）。 |
| `Prefill` | Boolean | 选填 | `false` | 是否将 ASR 中间结果提前送入大模型（降低延时但增加消耗）。 |

### 请求示例

你可参看以下示例，使用第三方大模型/Agent进行大模型调用：

```json
{
    "LLMConfig": {
            "Mode": "CustomLLM",
            "URL": "https://api.***.com/v1/chat/completions",
            "ModelName": "name1",
            "APIKey": "pat*****123231",
            "MaxTokens": 1024,
            "Temperature": 0,
            "TopP": 0.3,
            "SystemMessages": [
                "你是小宁，性格幽默又善解人意。你在表达时需简明扼要，有自己的观点。"
            ],
            "UserPrompts": [
                {
                    "Role": "user",
                    "Content": "你好"
                },
                {
                    "Role": "assistant",
                    "Content": "有什么可以帮到你的？"
                }
            ],
            "HistoryLength": 3
        }
}
```

json

### 注意事项

#### 1. 接口验证

若需要验证第三方大模型 URL 是否符合火山引擎标准，可前往[体验 Demo](https://demo.volcvideo.com/aigc/login?from=doc)，点击**修改 AI 设定**，选择**第三方模型**填入 URL 进行快速验证，若验证失败可前往[使用工具验证第三方LLM/Agent接口](https://www.volcengine.com/docs/6348/1399966#tool)下载验证工具查看详细报错。

#### 2. 对话上下文管理

使用符合火山接口标准的第三方大模型时，模型对话上下文生成逻辑由系统提示词（SystemMessages）、用户提示词（UserPrompts 和 UserMessages）和

 历史问题轮数（HistoryLength）共同控制。具体参看[第三方大模型上下文管理机制](https://www.volcengine.com/docs/6348/1581711#third)。

FAQ
---

### 如何更换大模型配置？

你需要先调用 `StopVoiceChat` 接口停止当前智能体任务，随后在 `StartVoiceChat.LLMConfig` 中传入新的大模型配置，并重新启动智能体任务。

### 对话过程中，一分钟前生成的结果，现在再提问，智能体已经不记得之前回答结果了怎么办？

可适当调大 `LLMConfig.HistoryLength` 参数，增大上下文存储长度。

