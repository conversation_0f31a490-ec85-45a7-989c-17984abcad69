Title: 回调格式参考--实时音视频-火山引擎

URL Source: https://www.volcengine.com/docs/6348/75124

Markdown Content:
回调格式参考--实时音视频-火山引擎

*   文档首页
/实时音视频/服务端 API 参考/服务端回调/回调格式参考

回调格式参考


![Image 1](blob:http://localhost/7511cd80c66a21d3576513d5323b9f98)文档反馈

![Image 2](blob:http://localhost/8791860fec2e7731d895208f5ceb1ced)问问助手

开通消息通知服务，并且订阅的事件发生后，你指定的 URL 会收到来自 RTC 服务端的消息回调。相关事件的信息包含在回调字段中。

参考本文，了解消息回调的格式。

### 回调行为

当你设置关注的回调事件发生时，RTC 服务端会向你指定接收回调的 URL 地址发起 HTTP POST 请求。具体回调信息包含在 request Body 中。

说明

接收回调的 URL 必须以域名开头。

 如果 URL 为 HTTPS 域名，请确保该域名 SSL 证书合法且完整。

### 回调字段

request body 中以 Json 格式包含回调信息，具体字段如下：

| 参数名 | 类型 | 示例值 | 描述 |
| --- | --- | --- | --- |
| EventType | **String** | `RecordStarted` | 事件类型 |
| EventData | **String** | / | 具体的事件内容，格式为 Json |
| EventTime | **String** | `1970-07-01T00:00:00Z` | 事件产生时间，日期格式遵守 ISO-8601 标准。 |
| EventId | **String** | / | 事件 Id，具有唯一性，可用于去重 |
| AppId | **String** | `Your_AppId` | RTC 应用的唯一标识 |
| Version | **String** | `2020-12-01` | 事件的版本号 |
| Signature | **String** | / | 回调签名。 |
| Nonce | **String** | / | 签名随机数 4位 |

> 注：
> 
> 
> *   关于目前支持消息回调的事件（`EventType`）和对应的事件内容 (`EventData`)，参看 [消息事件参考](https://www.volcengine.com/docs/6348/75125)。
> *    当前不保证回调事件的唯一性。建议你可以使用 `EventId` 对事件回调进行去重。

