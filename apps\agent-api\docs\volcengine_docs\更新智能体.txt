Title: 更新智能体 UpdateVoiceChat--实时音视频-火山引擎

URL Source: https://www.volcengine.com/docs/6348/1404671

Markdown Content:
更新智能体 UpdateVoiceChat--实时音视频-火山引擎


> 本文档 API 接口为最新版本接口，后续相关功能的新增都会在此更新，推荐使用最新版本接口。旧版接口文档请参考[历史版本](https://www.volcengine.com/docs/6348/1163751)。

在实时音视频通话场景中，若你需要对智能体进行操作，比如在智能体进行语音输出时进行打断，可以通过调用此接口实现。

使用说明
----

### 调用接口

关于调用接口的请求结构、公共参数、签名方法、返回结构，参看[调用方法](https://www.volcengine.com/docs/6348/1178320)。

注意事项
----

*   请求频率：单账号下 QPS 不得超过 60。
*   该接口请求接入地址仅支持 `rtc.volcengineapi.com`。

请求说明
----

*   请求方式：**POST**
*   请求地址：**https://rtc.volcengineapi.com?Action=UpdateVoiceChat&Version=2024-12-01**

调试
--

API Explorer

您可以通过 API Explorer 在线发起调用，无需关注签名生成过程，快速获取调用结果。

[去调试](https://api.volcengine.com/api-explorer/debug?action=UpdateVoiceChat&serviceCode=rtc&version=2024-12-01&groupName=%E6%99%BA%E8%83%BD%E4%BD%93)

请求参数
----

下表仅列出该接口特有的请求参数和部分公共参数。更多信息请见[公共参数](https://www.volcengine.com/docs/6348/1178321)。

### Query

| 参数 | 类型 | 是否必选 | 示例值 | 描述 |
| --- | --- | --- | --- | --- |
| Action | String | 是 | `UpdateVoiceChat` | 接口名称。当前 API 的名称为 `UpdateVoiceChat`。 |
| Version | String | 是 | `2024-12-01` | 接口版本。当前 API 的版本为 `2024-12-01`。 |

### Body

| 参数 | 类型 | 是否必选 | 示例值 | 描述 |
| --- | --- | --- | --- | --- |
| AppId | String | 是 | `661e****543cf` | 你的音视频应用的唯一标志，参看[创建 RTC 应用](https://www.volcengine.com/docs/6348/69865#%E6%AD%A5%E9%AA%A44%EF%BC%9A%E5%88%9B%E5%BB%BA-rtc-%E5%BA%94%E7%94%A8%EF%BC%8C%E8%8E%B7%E5%8F%96-appid)获取或创建 AppId。 |
| RoomId | String | 是 | `Room1` | 房间的 ID，是房间的唯一标志，由你自行定义、生成与维护，参数定义规则参看[参数赋值规范](https://www.volcengine.com/docs/6348/70114)。 |
| TaskId | String | 是 | `Task1` | 智能体任务 ID |
| Command | String | 是 | `interrupt` | 更新指令 * `interrupt`：打断智能体。 * `function`：传回工具调用信息指令。 * `ExternalTextToSpeech` ： 传入文本信息供 TTS 音频播放。使用方法参看[自定义语音播放](https://www.volcengine.com/docs/6348/1449206)。 * `ExternalPromptsForLLM`：传入自定义文本与用户问题拼接后送入 LLM。 * `ExternalTextToLLM`：传入外部问题送入 LLM。根据你设定的优先级决定替代用户问题或增加新一轮对话。 * `FinishSpeechRecognition`：触发新一轮对话。 |
| Message | String | 否 | ``` "{\"ToolCallID\":\"call_cx\",\"Content\":\"上海天气是台风\"}" ``` | 工具调用信息指令。 注意 1. `Command` 取值为 `function`、`ExternalTextToSpeech`、`ExternalPromptsForLLM`和`ExternalTextToLLM`时，`Message`必填。 2. 当 `Command` 取值为 `function`时，`Message` 格式需为 Json 转译字符串，例如： `"{\"ToolCallID\":\"call_cx\",\"Content\":\"上海天气是台风\"}"` 其他取值时格式为普通字符串，例如`你刚才的故事讲的真棒。"` 3. 当 `Command` 取值为 `ExternalTextToSpeech`时，`message` 传入内容建议不超过 200 个字符。 |
| InterruptMode | Integer | 否 | `1` | 传入文本信息或外部问题时，处理的优先级。 * `1`：高优先级。传入信息直接打断交互，进行处理。 * `2`：中优先级。等待当前交互结束后，进行处理。 * `3`：低优先级。如当前正在发生交互，直接丢弃 `Message` 传入的信息。 注意 当 `command` 为 `ExternalTextToSpeech` 或 `ExternalTextToLLM` 时为该参数必填。 |

返回参数
----

本接口无特有的返回参数。公共返回参数请见[返回结构](https://www.volcengine.com/docs/6348/1178322)。

 其中返回值 `Result` 仅在请求成功时返回 `ok`,失败时为空。

请求示例
----

```json
POST https://rtc.volcengineapi.com?Action=UpdateVoiceChat&Version=2024-12-01
{
    "AppId": "661e****543cf",
    "RoomId": "Room1",
    "TaskId": "User1",
    "Command": "interrupt"
}
```

json

返回示例
----

```json
{
    "Result": "ok",
    "ResponseMetadata": {
        "RequestId": "20230****10420",
        "Action": "UpdateAudioBot",
        "Service": "rtc",
        "Region": "cn-north-1"
    }
}
```

json

