Title: 语音识别配置--实时音视频-火山引擎

URL Source: https://www.volcengine.com/docs/6348/1581712

Markdown Content:
语音识别配置--实时音视频-火山引擎



在实时对话式 AI 场景中，你需要配置语音识别（ASR）能力，将用户的语音实时转换为文本，以便智能体进行理解和处理。RTC 提供一站式接入方案，只需在 `StartVoiceChat` 接口配置 `ASRConfig` 即可完成语音识别能力接入。本文将详细介绍不同服务的配置参数、注意事项及适用场景，帮助你根据需求选择合适的语音识别方案。

火山引擎提供以下两种语音识别接入方案，分别适用于不同场景：

*   **火山引擎流式语音识别大模型**：识别准确率更高，适用于对识别准确率要求较高的场景（如会议记录、智能客服等）。
*   **火山引擎流式语音识别**：识别速度更快，适用于响应速度要求高的语音控制场景。

火山引擎流式语音识别大模型
-------------

该接入方案由大模型能力加持，识别准确率更高，适用于对识别准确率要求较高的场景（如会议记录、智能客服等）。详细功能特性可参看[大模型语音识别](https://www.volcengine.com/docs/6561/1354871#%E5%8A%9F%E8%83%BD%E7%89%B9%E6%80%A7)。

### 核心配置参数

使用火山引擎流式语音识别大模型时，`StartVoiceChat.ASRConfig` 结构核心配置参数如下：

> 完整参数及说明可参看 [StartVoiceChat](https://www.volcengine.com/docs/6348/1558163) 接口。

| 参数名 | 类型 | 是否必填 | 示例值 | 说明 |
| --- | --- | --- | --- | --- |
| `Provider` | String | 是 | `volcano` | 语音识别服务提供商。该参数固定取值：`volcano`，表示仅支持火山引擎语音识别服务 |
| `ProviderParams.Mode` | String | 是 | `bigmodel` | 模型类型，固定取值 `bigmodel`。 |
| `ProviderParams.AppId` | String | 是 | `93****21` | 开通流式语音识别大模型服务的 App ID。从[豆包语音控制台](https://console.volcengine.com/speech/service/10011?)获取。 |
| `ProviderParams.AccessToken` | String | 是 | `MOaOaa_VQ6****1B34UHA4h5B` | 与 App ID 对应的鉴权 Token。从[豆包语音控制台](https://console.volcengine.com/speech/service/10011?)获取。 |
| `ProviderParams.ApiResourceId` | String | 否 | `volc.bigasr.sauc.duration` | 服务开通类型：`volc.bigasr.sauc.duration`（小时版）；`volc.bigasr.sauc.concurrent`（并发版）。默认 `duration`。 |
| `ProviderParams.StreamMode` | Integer | 否 | `0` | 输出模式： * `0`（流式输入流式输出，实时性高）； * `1`（流式输入非流式输出，准确率高）。默认 `0`。 |

### 请求示例代码

你可参看以下示例，使用火山引擎流式语音识别大模型进行语音识别：

```json
{
    "ASRConfig": {
        "Provider": "volcano",
        "ProviderParams": {
            "Mode": "bigmodel",
            "AppId": "93****21",
            "AccessToken": "MOaOaa_VQ6****1B34UHA4h5B",
            "ApiResourceId": "volc.bigasr.sauc.duration",
            "StreamMode": 0,
        },
        "VADConfig": {
            "SilenceTime": 800,
            "VolumeGain": 0.3
        },
        "TurnDetectionMode": 0
    }
}
```

json

### 注意事项

#### 提升语音识别准确性

在使用中，如果你觉得识别结果不够准确，可通过调整音量增强、添加热词、替换词等方式提升识别准确率。详细使用说明参看[如何提升语音识别准确性？](https://www.volcengine.com/docs/6348/1563620)。

#### 输出模式选择建议

你可以使用 `StreamMode` 参数控制语音识别大模型输出模式。

*   取值为 `0` 时，表示流式输入流式输出。此时识别结果会分段、实时地返回。该模式下识别速度更快，适用于实时字幕场景。
*   取值为 `1` 时，表示流式输入非流式输出。即在完整接收并处理完整个语音片段后，一次性返回最终的识别结果。该模式下识别准确率更高，适用于不需要语音打断功能的场景。

#### 打断配置优化

*   若需通过关键词触发打断（如“停止”），建议将 `InterruptSpeechDuration` 设为 `0`，避免自动阈值覆盖关键词触发逻辑。
*   背景噪音较多时，可通过 `VolumeGain` 调节音量增益值，当低增益值可减少噪音引起的 ASR 错误识别，减少误触发。

#### 处理较长音频流式断句问题

在使用流式语音识别大模型时，如果智能体输出音频过长，可能会出现输出未结束便被截断。为避免这种情况，你可以使用[手动触发新一轮对话](https://www.volcengine.com/docs/6348/1544164)功能手动控制智能体结束输出的时机。

火山引擎流式语音识别
----------

该接入方案采用业端到端算法模型，识别速度更快，适用于响应速度要求高的语音控制场景。详细功能特性可参看[语音识别](https://www.volcengine.com/docs/6561/109880#%E5%8A%9F%E8%83%BD%E7%89%B9%E6%80%A7)。

### 核心配置参数

使用 `StartVoiceChat` 接口配置时，需通过 `Config.ASRConfig` 结构设置参数。以下为核心配置参数说明：

> 完整参数及说明可参看 [StartVoiceChat 接口文档](https://www.volcengine.com/docs/6348/1558163#8XY1OLpi)。

| 参数名 | 类型 | 是否必填 | 示例值 | 说明 |
| --- | --- | --- | --- | --- |
| `Provider` | String | 是 | `volcano` | 语音识别服务提供商，固定取值 `volcano`（仅支持火山引擎服务）。 |
| `TurnDetectionMode` | Integer | 否 | `0` | 对话触发方式： * `0`（服务端自动检测完整语句触发）； * `1`（手动触发）。 默认 `0`。 |
| `ProviderParams.Mode` | String | 是 | `smallmodel` | 模型类型，固定取值 `smallmodel`。 |
| `ProviderParams.AppId` | String | 是 | `93****21` | 开通流式语音识别服务的 App ID。从[豆包语音控制台](https://console.volcengine.com/speech/service/16?)获取。 |
| `ProviderParams.Cluster` | String | 是 | `volcengine_streaming_common` | 服务集群标识。从[豆包语音控制台](https://console.volcengine.com/speech/service/16?)开通后获取。 |

### 请求示例代码

你可参看以下示例，使用火山引擎流式语音识别进行语音识别：

```json
{
    "ASRConfig": {
        "Provider": "volcano",
        "ProviderParams": {
            "Mode": "smallmodel",
            "AppId": "93****21",
            "Cluster": "volcengine_streaming_common"
        },
        "VADConfig": {
            "SilenceTime": 800,
            "VolumeGain": 0.3
        },
        "TurnDetectionMode": 0
    }
}
```

json

### 注意事项

#### 打断配置优化

*   若需通过关键词触发打断（如“停止”），建议将 `InterruptSpeechDuration` 设为 `0`，避免自动阈值覆盖关键词触发逻辑。
*   背景噪音较多时，可通过 `VolumeGain` 调节音量增益值，当低增益值可减少噪音引起的 ASR 错误识别，减少误触发。

