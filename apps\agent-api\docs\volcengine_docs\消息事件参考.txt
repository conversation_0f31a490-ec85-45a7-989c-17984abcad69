Title: 消息事件参考--实时音视频-火山引擎

URL Source: https://www.volcengine.com/docs/6348/75125

Markdown Content:

消息通知服务支持的事件有：

功能	EventType	事件描述
音频流	UserAudioStreamStart	音频流开始推送
UserAudioStreamStop	音频流结束推送
视频流	UserVideoStreamStart	视频流开始推送
UserVideoStreamStop	视频流结束推送
屏幕音频流	UserScreenAudioStreamStart	屏幕音频流开始推送
UserScreenAudioStreamStop	屏幕音频流结束推送
屏幕视频流	UserScreenVideoStreamStart	屏幕视频流开始推送
UserScreenVideoStreamStop	屏幕视频流结束推送
云端录制	RecordStarted	录制任务开始
RecordStopped(2020-12-01)	录制任务结束（2020-12-01）
RecordStopped(2022-06-01)	录制任务结束（2022-06-01）
RecordStopped(2023-06-01)	录制任务结束（2023-06-01）
RecordStopped(2023-11-01)	录制任务结束（2023-11-01）
RecordUploadStarted	上传任务启动
RecordUploadProcessing	上传进度
RecordUploadDone	已完成上传至存储
RecordUploadBackuped	上传失败转存备份
RecordAudioStreamStateChanged	录制音频流状态变化
RecordVideoStreamStateChanged	录制视频流状态变化
RecordSyncUploadStarted	录制生成 m3u8 文件且上传成功
RecordSyncUploadFailed	录制生成 m3u8 文件且上传失败
房间	UserJoinRoom	可见用户进房
UserLeaveRoom	可见用户退房
InvisibleUserJoinRoom	不可见用户进房
InvisibleUserLeaveRoom	不可见用户退房
RoleChangeInvisible2Visible	用户身份切换（不可见 -> 可见）
RoleChangeVisible2Invisible	用户身份切换（可见 -> 不可见）
RoomCreate	房间创建
RoomDestroy	房间销毁
音频切片	SegmentRealTimeData(2020-12-01)	切片结果（2020-12-01）
SegmentRealTimeData(2022-06-01)	切片结果（2022-06-01）
SegmentRealTimeData(2023-11-01)	切片结果（2023-11-01）
抽帧截图	SnapshotRealTimeData(2020-12-01)	截图结果（2020-12-01）
SnapshotRealTimeData(2022-06-01)	截图结果（2022-06-01）
SnapshotRealTimeData(2023-11-01)	截图结果（2023-11-01）
在线媒体流	RelayStreamStateChanged	在线媒体流任务状态变化
云录屏	Webcast	云录屏任务状态变化
转推直播	TranscodeStarted	转推直播任务开始
TranscodeStopped	转推直播任务结束
TranscodeUpdated	转推直播任务更新
TranscodeStateChanged	转推直播任务状态变化
公共流	PushPublicStream	公共流任务状态变化
歌曲查询	MusicRemoved	曲库下架歌曲列表
互动白板文件转码	WbTranscodeProgressChanged	文件转码进度
WbTranscodeFinished	文件转码结束
实时对话式 AI	VoiceChat	智能体任务状态变化。
音频流相关
UserAudioStreamStart

EventType

UserAudioStreamStart

事件详情

音频流开始推送。

EventData

参数名	类型	示例值	描述
RoomId	String	Your_RoomId	房间 ID
UserId	String	Your_UserId	用户 ID
DeviceType	String	android、ios、web、mac、windows	终端类型
Timestamp	Int64	1611736812853	该事件在 RTC 服务器上发生的 Unix 时间戳 (ms)

参数示例

{
    "RoomId": "Your_RoomId",
    "UserId": "Your_UserId",
    "DeviceType": "android",
    "Timestamp": 1611736812853
}

json
UserAudioStreamStop

EventType

UserAudioStreamStop

事件详情

音频流结束推送。

EventData

参数名	类型	示例值	描述
RoomId	String	Your_RoomId	房间 ID
UserId	String	Your_UserId	用户 ID
DeviceType	String	android、ios、web、mac、windows	终端类型
Reason	String		音频流结束推送原因，参看Reason
Timestamp	Int64	1611736812853	该事件在 RTC 服务器上发生的 Unix 时间戳 (ms)


Reason

值	含义
StreamStop	正常流停止
LeaveRoom	用户离开房间导致流停止
BannedByAdmin	服务端封禁该音频流导致流停止

参数示例

{
    "RoomId": "Your_RoomId",
    "UserId": "Your_UserId",
    "DeviceType": "android",
    "Reason": "StreamStop",
    "Timestamp": 1611736812853
}

json
视频流相关
UserVideoStreamStart

EventType

UserVideoStreamStart

事件详情

视频流开始推送。

EventData

参数名	类型	示例值	描述
RoomId	String	Your_RoomId	房间 ID
UserId	String	Your_UserId	用户 ID
DeviceType	String	android、ios、web、mac、windows	终端类型
Timestamp	Int64	1611736812853	该事件在 RTC 服务器上发生的 Unix 时间戳 (ms)

参数示例

{
    "RoomId": "Your_RoomId",
    "UserId": "Your_UserId",
    "DeviceType": "android",
    "Timestamp": 1611736812853
}

json
UserVideoStreamStop

EventType

UserVideoStreamStop

事件详情

视频流结束推送。

EventData

参数名	类型	示例值	描述
RoomId	String	Your_RoomId	房间 ID
UserId	String	Your_UserId	用户 ID
DeviceType	String	android、ios、web、mac、windows	终端类型
Reason	String		视频流结束推送原因，参看Reason
Timestamp	Int64	1611736812853	该事件在 RTC 服务器上发生的 Unix 时间戳 (ms)


Reason

值	含义
StreamStop	正常流停止
LeaveRoom	用户离开房间导致流停止
BannedByAdmin	服务端封禁该视频流导致流停止

参数示例

{
    "RoomId": "Your_RoomId",
    "UserId": "Your_UserId",
    "DeviceType": "android",
    "Reason": "StreamStop",
    "Timestamp": 1611736812853
}

json
屏幕音频流相关
UserScreenAudioStreamStart

EventType

UserScreenAudioStreamStart

事件详情

屏幕音频流开始推送。

EventData

参数名	类型	示例值	描述
RoomId	String	Your_RoomId	房间 ID
UserId	String	Your_UserId	用户 ID
DeviceType	String	android、ios、web、mac、windows	终端类型
Timestamp	Int64	1611736812853	该事件在 RTC 服务器上发生的 Unix 时间戳 (ms)

参数示例

{
    "RoomId": "Your_RoomId",
    "UserId": "Your_UserId",
    "DeviceType": "android",
    "Timestamp": 1611736812853
}

json
UserScreenAudioStreamStop

EventType

UserScreenAudioStreamStop

事件详情

屏幕音频流结束推送。

EventData

参数名	类型	示例值	描述
RoomId	String	Your_RoomId	房间 ID
UserId	String	Your_UserId	用户 ID
DeviceType	String	android、ios、web、mac、windows	终端类型
Reason	String		屏幕音频流结束推送原因，参看Reason
Timestamp	Int64	1611736812853	该事件在 RTC 服务器上发生的 Unix 时间戳 (ms)

Reason

值	含义
StreamStop	正常流停止
LeaveRoom	用户离开房间导致流停止
BannedByAdmin	服务端封禁该屏幕音频流导致流停止

参数示例

{
    "RoomId": "Your_RoomId",
    "UserId": "Your_UserId",
    "DeviceType": "android",
    "Reason": "StreamStop",
    "Timestamp": 1611736812853
}

json
屏幕视频流相关
UserScreenVideoStreamStart

EventType

UserScreenVideoStreamStart

事件详情

屏幕视频流开始推送。

EventData

参数名	类型	示例值	描述
RoomId	String	Your_RoomId	房间 ID
UserId	String	Your_UserId	用户 ID
DeviceType	String	android、ios、web、mac、windows	终端类型
Timestamp	Int64	1611736812853	该事件在 RTC 服务器上发生的 Unix 时间戳 (ms)

参数示例

{
    "RoomId": "Your_RoomId",
    "UserId": "Your_UserId",
    "DeviceType": "android",
    "Timestamp": 1611736812853
}

json
UserScreenVideoStreamStop

EventType

UserScreenVideoStreamStop

事件详情

屏幕视频流结束推送。

注：在 Electron 1.4.0 版本中结束屏幕视频流推送时，不会触发此回调。

EventData

参数名	类型	示例值	描述
RoomId	String	Your_RoomId	房间 ID
UserId	String	Your_UserId	用户 ID
DeviceType	String	android、ios、web、mac、windows	终端类型
Reason	String		屏幕视频流结束推送原因，参看Reason
Timestamp	Int64	1611736812853	该事件在 RTC 服务器上发生的 Unix 时间戳 (ms)

Reason

值	含义
StreamStop	正常流停止
LeaveRoom	用户离开房间导致流停止
BannedByAdmin	服务端封禁该屏幕视频流导致流停止

参数示例

{
    "RoomId": "Your_RoomId",
    "UserId": "Your_UserId",
    "DeviceType": "android",
    "Reason": "StreamStop",
    "Timestamp": 1611736812853
}

json
录制相关
RecordStarted

EventType

RecordStarted

事件详情

录制任务开始。

不同版本录制功能在任务开始时均触发此回调。

无论录制任务是通过何种方式启动的（调用 OpenAPI 启动/在控制台上配置自动录制），在录制任务开始时，你都会收到录制任务开始的消息通知。

EventData

参数名	类型	示例值	描述
AppId	String	Your_AppId	音视频应用的唯一标识
BusinessId	String	Your_BusinessId	业务标识
RoomId	String	Your_RoomId	用户创建的房间 ID，房间的唯一标识
TaskId	String	Your_TaskId	任务 ID，同一房间内的录制任务通过 TaskId 来区分
Code	Int32	0	任务状态，值的集合为{0，1}。其中，0 表示成功, 1 表示失败
ErrorMessage	String		具体的错误信息。当 Code 不为 0 时，ErrorMessage 会显示具体的错误

参数示例

{
    "AppId": "Your_AppId",
    "BusinessId": "Your_BusinessId",    
    "RoomId": "Your_RoomId",
    "TaskId": "Your_TaskId",
    "Code": 0,
    "ErrorMessage": ""
}

json
RecordStopped(2020-12-01)

EventType

RecordStopped

事件详情

录制任务结束。

2020-12-01 版本录制功能在任务结束时触发此回调。

无论录制任务是通过何种方式启动的（调用 OpenAPI 启动/在控制台上配置自动录制），在录制任务结束时，你都会收到录制任务结束的消息通知。

EventData

参数名	类型	示例值	描述
AppId	String	Your_AppId	音视频应用的唯一标识
BusinessId	String	Your_BusinessId	业务标识
RoomId	String	Your_RoomId	用户创建的房间 ID，房间的唯一标识
TaskId	String	Your_TaskId	任务 ID，同一房间内的录制任务通过 TaskId 来区分
Code	Int32	0	任务状态。
0：任务结束，录制文件上传目标存储平台成功。
1：任务结束，录制文件上传目标存储平台失败、备份存储失败。
2：任务结束，录制文件上传目标存储平台失败，备份存储成功。

ErrorMessage	String		具体的错误信息。当 Code 不为 0 时，ErrorMessage 会显示具体的错误
RecordFileList	Array of RecordFile		录制生成的音视频文件列表

RecordFile

参数名称	类型	描述
Vid	String	文件在点播平台的唯一标识。你可以根据 vid 可以在点播平台上找到对应的文件。
Duration	Uint64	文件时长，单位为毫秒。
Size	Uint64	文件大小，单位为 byte。
StartTime	Uint64	文件开始录制的 UTC 时间，单位为毫秒。
StreamList	Array of Stream	录制文件中包含流的列表。
VideoCodec	String	视频录制编码协议。默认值为 0，可以取 0 或 1。取 0 时使用 H.264,取 1 时使用 ByteVC1 编码器。
AudioCodec	String	音频录制编码器
VideoWidth	Int	录制视频宽度，单位为像素
VideoHeight	Int	录制视频高度，单位为像素

Stream

参数名称	类型	必填	示例值	描述
Index	Uint32	否	0	在自定义布局中，使用 Index 对流进行标志。后续在 Layout.regions.StreamIndex 中，你需要使用 Index 指定对应流的布局设置。
UserId	String	是	Your_UserId	用户Id，表示这个流所属的用户。
StreamType	Uint32	否	0	流的类型，值可以取0或1，默认值为0。0表示普通音视频流，1表示屏幕流。

注意：如果录制结束回调 Code=0，但是 RecordFileList 为空，没有生成录制文件，请联系技术支持排查具体原因。

参数示例

{
    "AppId": "Your_AppId",
    "BusinessId": "Your_BusinessId",
    "RoomId": "Your_RoomId",
    "TaskId": "Your_TaskId",
    "Code": 0,
    "ErrorMessage": "",
    "RecordFileList": [
        {
            "Vid": "Your_Vid",
            "Duration": 57472,
            "Size": 5018305,
            "StartTime": 1611736812853,
            "StreamList": [
                {
                    "UserId": "TestUserId",
                    "StreamType": 0
                }
            ],
            "VideoCodec": "h264",
            "AudioCodec": "aac",
            "VideoWidth": 640,
            "VideoHeight": 360
        }
    ]
}

json
RecordStopped(2022-06-01)

EventType

RecordStopped

事件详情

录制任务结束。

2022-06-01 版本录制功能在任务结束时触发此回调。

无论录制任务是通过何种方式启动的（调用 OpenAPI 启动/在控制台上配置自动录制），在录制任务结束时，你都会收到录制任务结束的消息通知。

EventData

参数名	类型	示例值	描述
AppId	String	Your_AppId	音视频应用的唯一标识
BusinessId	String	Your_BusinessId	业务标识
RoomId	String	Your_RoomId	用户创建的房间 ID，房间的唯一标识
TaskId	String	Your_TaskId	任务 ID，同一房间内的录制任务通过 TaskId 来区分
Code	Int32	0	任务状态。
0：任务结束，录制文件上传目标存储平台成功。
1：任务结束，录制文件上传目标存储平台失败、备份存储失败。
2：任务结束，录制文件上传目标存储平台失败，备份存储成功。

ErrorMessage	String	/	具体的错误信息。当 Code 不为 0 时，ErrorMessage 会显示具体的错误
RecordFileList	Array of RecordFile	/	录制生成的音视频文件列表

RecordFile

参数名称	类型	描述
Vid	String	文件在火山引擎视频点播 VOD  平台的唯一标识。你可以根据 vid 在点播平台上找到对应的文件。仅在你选择配置存储到 Vod 平台时，此参数有效。
ObjectKey	String	文件在对象存储平台中的完整路径，如abc/efg/123.mp4。仅在你选择配置存储到对象存储平台时，此参数有效。
Duration	Uint64	文件时长，单位为毫秒。
Size	Uint64	文件大小，单位为 byte。
StartTime	Uint64	文件开始录制的 UTC 时间，单位为毫秒。
StreamList	Array of Stream	录制文件中包含流的列表。
VideoCodec	String	视频录制编码协议
AudioCodec	String	音频录制编码器
VideoWidth	Int	录制视频宽度，单位为像素
VideoHeight	Int	录制视频高度，单位为像素

Stream

参数名称	类型	必填	示例值	描述
Index	Uint32	否	0	在自定义布局中，使用 Index 对流进行标志。后续在 Layout.regions.StreamIndex 中，你需要使用 Index 指定对应流的布局设置。
UserId	String	是	Your_UserId	用户Id，表示这个流所属的用户。
StreamType	Uint32	否	0	流的类型，值可以取0或1，默认值为0。0表示普通音视频流，1表示屏幕流。

注意：如果录制结束回调 Code=0，但是 RecordFileList 为空，没有生成录制文件，请联系技术支持排查具体原因。

参数示例

{
    "AppId": "Your_AppId",
    "BusinessId": "Your_BusinessId",
    "RoomId": "Your_RoomId",
    "TaskId": "Your_TaskId",
    "Code": 0,
    "ErrorMessage": "",
    "RecordFileList": [
        {
            "Vid": "Your_Vid",
            "ObjectKey": "TestObjectKey",
            "Duration": 57472,
            "Size": 5018305,
            "StartTime": 1611736812853,
            "StreamList": [
                {
                    "UserId": "TestUserId",
                    "StreamType": 0
                }
            ],
            "VideoCodec": "h264",
            "AudioCodec": "aac",
            "VideoWidth": 640,
            "VideoHeight": 360
        }
    ]
}

json
RecordStopped(2023-06-01)

EventType

RecordStopped

事件详情

录制任务结束。

2023-06-01 版本录制功能在任务结束时触发此回调。

无论录制任务是通过何种方式启动的（调用 OpenAPI 启动/在控制台上配置自动录制），在录制任务结束时，你都会收到录制任务结束的消息通知。

EventData

参数名	类型	示例值	描述
AppId	String	Your_AppId	音视频应用的唯一标识
BusinessId	String	Your_BusinessId	业务标识
RoomId	String	Your_RoomId	用户创建的房间 ID，房间的唯一标识
TaskId	String	Your_TaskId	任务 ID，同一房间内的录制任务通过 TaskId 来区分
Code	Int32	0	任务状态。
0：任务结束，录制文件上传目标存储平台成功。
1：任务结束，录制文件上传目标存储平台失败、备份存储失败。
2：任务结束，录制文件上传目标存储平台失败，备份存储成功。

ErrorMessage	String	/	具体的错误信息。当 Code 不为 0 时，ErrorMessage 会显示具体的错误
RecordFileList	Array of RecordFile	/	录制生成的音视频文件列表

RecordFile

参数名称	类型	描述
Vid	String	文件在火山引擎视频点播 VOD  平台的唯一标识。你可以根据 vid 在点播平台上找到对应的文件。仅在你选择配置存储到 Vod 平台时，此参数有效。
ObjectKey	String	文件在对象存储平台中的完整路径，如abc/efg/123.mp4。仅在你选择配置存储到对象存储平台时，此参数有效。
Duration	Uint64	文件时长，单位为毫秒。
Size	Uint64	文件大小，单位为 byte。
StartTime	Uint64	文件开始录制的 UTC 时间，单位为毫秒。
StreamList	Array of Stream	录制文件中包含流的列表。
VideoCodec	String	视频录制编码协议
AudioCodec	String	音频录制编码器
VideoWidth	Int	录制视频宽度，单位为像素
VideoHeight	Int	录制视频高度，单位为像素

Stream

参数名称	类型	必填	示例值	描述
Index	Uint32	否	0	在自定义布局中，使用 Index 对流进行标志。后续在 Layout.regions.StreamIndex 中，你需要使用 Index 指定对应流的布局设置。
UserId	String	是	Your_UserId	用户Id，表示这个流所属的用户。
StreamType	Uint32	否	0	流的类型，值可以取0或1，默认值为0。0表示普通音视频流，1表示屏幕流。

注意：如果录制结束回调 Code=0，但是 RecordFileList 为空，没有生成录制文件，请联系技术支持排查具体原因。

参数示例

{
    "AppId": "Your_AppId",
    "BusinessId": "Your_BusinessId",
    "RoomId": "Your_RoomId",
    "TaskId": "Your_TaskId",
    "Code": 0,
    "ErrorMessage": "",
    "RecordFileList": [
        {
            "Vid": "Your_Vid",
            "ObjectKey": "TestObjectKey",
            "Duration": 57472,
            "Size": 5018305,
            "StartTime": 1611736812853,
            "StreamList": [
                {
                    "UserId": "TestUserId",
                    "StreamType": 0
                }
            ],
            "VideoCodec": "h264",
            "AudioCodec": "aac",
            "VideoWidth": 640,
            "VideoHeight": 360
        }
    ]
}

json
RecordStopped(2023-11-01)

EventType

RecordStopped

事件详情

录制任务结束。
2023-11-01 版本录制功能在任务结束时触发此回调。

无论录制任务是通过何种方式启动的（调用 OpenAPI 启动/在控制台上配置自动录制），在录制任务结束时，你都会收到录制任务结束的消息通知。

EventData

参数名	类型	示例值	描述
AppId	String	Your_AppId	音视频应用的唯一标识
BusinessId	String	Your_BusinessId	业务标识
RoomId	String	Your_RoomId	用户创建的房间 ID，房间的唯一标识
TaskId	String	Your_TaskId	任务 ID，同一房间内的录制任务通过 TaskId 来区分
Code	Int32	0	任务状态。
0：任务结束，录制文件上传目标存储平台成功。
1：任务结束，录制文件上传目标存储平台失败、备份存储失败。
2：任务结束，录制文件上传目标存储平台失败，备份存储成功。

ErrorMessage	String	/	具体的错误信息。当 Code 不为 0 时，ErrorMessage 会显示具体的错误
RecordFileList	Array of RecordFile	/	录制生成的音视频文件列表

RecordFile

参数名称	类型	描述
Vid	String	文件在火山引擎视频点播 VOD  平台的唯一标识。你可以根据 vid 在点播平台上找到对应的文件。仅在你选择配置存储到 Vod 平台时，此参数有效。
ObjectKey	String	文件在对象存储平台中的完整路径，如abc/efg/123.mp4。仅在你选择配置存储到对象存储平台时，此参数有效。
Duration	Uint64	文件时长，单位为毫秒。
Size	Uint64	文件大小，单位为 byte。
StartTime	Uint64	文件开始录制的 UTC 时间，单位为毫秒。
StreamList	Array of Stream	录制文件中包含流的列表。
VideoCodec	String	视频录制编码协议
AudioCodec	String	音频录制编码器
VideoWidth	Int	录制视频宽度，单位为像素
VideoHeight	Int	录制视频高度，单位为像素

Stream

参数名称	类型	必填	示例值	描述
Index	Uint32	否	0	在自定义布局中，使用 Index 对流进行标志。后续在 Layout.regions.StreamIndex 中，你需要使用 Index 指定对应流的布局设置。
UserId	String	是	Your_UserId	用户Id，表示这个流所属的用户。
StreamType	Uint32	否	0	流的类型，值可以取0或1，默认值为0。0表示普通音视频流，1表示屏幕流。

注意：如果录制结束回调 Code=0，但是 RecordFileList 为空，没有生成录制文件，请联系技术支持排查具体原因。

参数示例

{
    "AppId": "Your_AppId",
    "BusinessId": "Your_BusinessId",
    "RoomId": "Your_RoomId",
    "TaskId": "Your_TaskId",
    "Code": 0,
    "ErrorMessage": "",
    "RecordFileList": [
        {
            "Vid": "Your_Vid",
            "ObjectKey": "TestObjectKey",
            "Duration": 57472,
            "Size": 5018305,
            "StartTime": 1611736812853,
            "StreamList": [
                {
                    "UserId": "TestUserId",
                    "StreamType": 0
                }
            ],
            "VideoCodec": "h264",
            "AudioCodec": "aac",
            "VideoWidth": 640,
            "VideoHeight": 360
        }
    ]
}

json
RecordUploadStarted

EventType

RecordUploadStarted

事件详情

上传任务启动。

不同版本录制功能在上传任务启动时均触发此回调。

EventData

参数名	类型	示例值	描述
AppId	String	Your_AppId	音视频应用的唯一标识
BusinessId	String	Your_BusinessId	业务标识
RoomId	String	Your_RoomId	用户创建的房间 ID，房间的唯一标识
TaskId	String	Your_TaskId	任务 ID，同一房间内的录制任务通过 TaskId 来区分
Code	Int32	0	任务状态，值的集合为{0，1}。其中，0 表示成功, 1 表示失败
ErrorMessage	String		具体的错误信息。当 Code 不为 0 时，ErrorMessage 会显示具体的错误

参数示例

{
    "AppId": "Your_AppId",
    "BusinessId": "Your_BusinessId",    
    "RoomId": "Your_RoomId",
    "TaskId": "Your_TaskId",
    "Code": 1,
    "ErrorMessage": ""
}

json
RecordUploadProcessing

EventType

RecordUploadProcessing

事件详情
上传任务进度。

上传进程持续一分钟及以上，才会有此回调。每隔一分钟上传一次。

不同版本录制功能在上传任务进度发生变化时均触发此回调。

EventData

参数名	类型	示例值	描述
AppId	String	Your_AppId	音视频应用的唯一标识
BusinessId	String	Your_BusinessId	业务标识
RoomId	String	Your_RoomId	用户创建的房间 ID，房间的唯一标识
TaskId	String	Your_TaskId	任务 ID，同一房间内的录制任务通过 TaskId 来区分
Code	Int32	0	任务状态，值的集合为{0，1}。其中，0 表示成功, 1 表示失败
ErrorMessage	String		具体的错误信息。当 Code 不为 0 时，ErrorMessage 会显示具体的错误
Progress	Int	100	0 到 10000 之间的数字，当前已上传文件与已录制的文件的比例乘以 10000。这个数字是不断变动的，录制完成后，到达 10000 表示上传完成。

参数示例

{
    "AppId": "Your_AppId",
    "BusinessId": "Your_BusinessId",    
    "RoomId": "Your_RoomId",
    "TaskId": "Your_TaskId",
    "Code": 1,
    "ErrorMessage": "",
    "Progress" : 100
}

json
RecordUploadDone

EventType

RecordUploadDone

事件详情

录制文件已上传至存储平台。

不同版本录制功能在录制文件已上传至存储平台时均触发此回调。

EventData

参数名	类型	示例值	描述
AppId	String	Your_AppId	音视频应用的唯一标识
BusinessId	String	Your_BusinessId	业务标识
RoomId	String	Your_RoomId	用户创建的房间 ID，房间的唯一标识
TaskId	String	Your_TaskId	任务 ID，同一房间内的录制任务通过 TaskId 来区分
Code	Int32	0	任务状态，值的集合为{0，1}。其中，0 表示成功, 1 表示失败
ErrorMessage	String		具体的错误信息。当 Code 不为 0 时，ErrorMessage 会显示具体的错误

参数示例

{
    "AppId": "Your_AppId",
    "BusinessId": "Your_BusinessId",    
    "RoomId": "Your_RoomId",
    "TaskId": "Your_TaskId",
    "Code": 1,
    "ErrorMessage": ""
}

json
RecordUploadBackuped

EventType

RecordUploadBackuped

事件详情

录制文件上传失败转存备份

不同版本录制功能在录制文件上传失败转存备份时均触发此回调。

备份使用的 TOS Bucket 需要开启版本控制，防止文件名重复导致文件被覆盖。

EventData

参数名	类型	示例值	描述
AppId	String	Your_AppId	音视频应用的唯一标识
BusinessId	String	Your_BusinessId	业务标识
RoomId	String	Your_RoomId	用户创建的房间 ID，房间的唯一标识
TaskId	String	Your_TaskId	任务 ID，同一房间内的录制任务通过 TaskId 来区分
Code	Int32	0	任务状态，值的集合为{0，1}。其中，0 表示成功, 1 表示失败
ErrorMessage	String		具体的错误信息。当 Code 不为 0 时，ErrorMessage 会显示具体的错误

参数示例

{
    "AppId": "Your_AppId",
    "BusinessId": "Your_BusinessId",    
    "RoomId": "Your_RoomId",
    "TaskId": "Your_TaskId",
    "Code": 1,
    "ErrorMessage": ""
}

json
RecordAudioStreamStateChanged

EventType

RecordAudioStreamStateChanged

事件详情

录制音频流状态变化

EventData

参数名	类型	示例值	描述
AppId	String	Your_AppId	音视频应用的唯一标识
BusinessId	String	Your_BusinessId	业务标识
RoomId	String	Your_RoomId	用户创建的房间 ID，房间的唯一标识
TaskId	String	Your_TaskId	任务 ID，同一房间内的录制任务通过 TaskId 来区分
UserId	String	Your_UserId	用户 ID
StreamType	Int	0	音频流类型。
0：麦克风采集流
1：屏幕流

State	Int	0	任务状态。
0：音频流停止发布
1：音频流正在发布

Timestamp	Int64	1611736812853	该事件在 RTC 服务器上发生的 Unix 时间戳 (ms)

参数示例

{
    "AppId": "Your_AppId",
    "BusinessId": "Your_BusinessId",    
    "RoomId": "Your_RoomId",
    "TaskId": "Your_TaskId",
    "UserId": "Your_UserId",
    "StreamType": 0,
    "State": 0,  
    "Timestamp": 1611736812853     
}

json
RecordVideoStreamStateChanged

EventType

RecordVideoStreamStateChanged

事件详情

录制视频流状态变化

EventData

参数名	类型	示例值	描述
AppId	String	Your_AppId	音视频应用的唯一标识
BusinessId	String	Your_BusinessId	业务标识
RoomId	String	Your_RoomId	用户创建的房间 ID，房间的唯一标识
TaskId	String	Your_TaskId	任务 ID，同一房间内的录制任务通过 TaskId 来区分
UserId	String	Your_UserId	用户 ID
StreamType	Int	0	视频流类型。
0：摄像头采集流
1：屏幕流

State	Int	0	任务状态。
0：视频流停止发布
1：视频流正在发布

Timestamp	Int64	1611736812853	该事件在 RTC 服务器上发生的 Unix 时间戳 (ms)

参数示例

{
    "AppId": "Your_AppId",
    "BusinessId": "Your_BusinessId",    
    "RoomId": "Your_RoomId",
    "TaskId": "Your_TaskId",
    "UserId": "Your_UserId,
    "StreamType": 0,
    "State": 0,  
    "Timestamp": 1611736812853     
}

json
RecordSyncUploadStarted

EventType

RecordSyncUploadStarted

事件详情

录制生成 m3u8 文件且上传至指定存储平台。
此事件仅在第一次上传成功后回调。

EventData

参数名	类型	示例值	描述
AppId	String	Your_AppId	音视频应用的唯一标识
BusinessId	String	Your_BusinessId	业务标识
RoomId	String	Your_RoomId	用户创建的房间 ID，房间的唯一标识
TaskId	String	Your_TaskId	任务 ID，同一房间内的录制任务通过 TaskId 来区分
Code	Int32	0	任务状态，值的集合为{0，1}。其中，0 表示成功, 1 表示失败
ErrorMessage	String		具体的错误信息。当 Code 不为 0 时，ErrorMessage 会显示具体的错误
RecordFileList	Array of RecordFile		录制生成的音视频文件列表

RecordFile

参数名称	类型	描述
Vid	String	文件在火山引擎视频点播 VOD  平台的唯一标识。你可以根据 vid 在点播平台上找到对应的文件。仅在你选择配置存储到 Vod 平台时，此参数有效。
ObjectKey	String	文件在对象存储平台中的完整路径，如abc/efg/123.mp4。仅在你选择配置存储到对象存储平台时，此参数有效。
StartTime	Uint64	文件开始录制的 UTC 时间，单位为毫秒。
StreamList	Array of Stream	录制文件中包含流的列表。
VideoCodec	String	视频录制编码协议
AudioCodec	String	音频录制编码器
VideoWidth	Int	录制视频宽度，单位为像素
VideoHeight	Int	录制视频高度，单位为像素

Stream

参数名称	类型	必填	示例值	描述
Index	Uint32	否	0	在自定义布局中，使用 Index 对流进行标志。后续在 Layout.regions.StreamIndex 中，你需要使用 Index 指定对应流的布局设置。
UserId	String	是	Your_UserId	用户Id，表示这个流所属的用户。
StreamType	Uint32	否	0	流的类型，值可以取0或1，默认值为0。0表示普通音视频流，1表示屏幕流。

参数示例

{
    "AppId": "Your_AppId",
    "BusinessId": "Your_BusinessId",
    "RoomId": "Your_RoomId",
    "TaskId": "Your_TaskId",
    "Code": 0,
    "ErrorMessage": "",
    "RecordFileList": [
        {
            "Vid": "Your_Vid",
            "ObjectKey": "TestObjectKey",
            "StartTime": 1611736812853,
            "StreamList": [
                {
                    "UserId": "TestUserId",
                    "StreamType": 0
                }
            ],
            "VideoCodec": "h264",
            "AudioCodec": "aac",
            "VideoWidth": 640,
            "VideoHeight": 360
        }
    ]
}

json
RecordSyncUploadFailed

EventType

RecordSyncUploadFailed

事件详情
录制生成 m3u8 文件且上传至指定存储平台失败。

EventData

参数名	类型	示例值	描述
AppId	String	Your_AppId	音视频应用的唯一标识
BusinessId	String	Your_BusinessId	业务标识
RoomId	String	Your_RoomId	用户创建的房间 ID，房间的唯一标识
TaskId	String	Your_TaskId	任务 ID，同一房间内的录制任务通过 TaskId 来区分
Code	Int32	0	任务状态，值的集合为{0，1}。其中，0 表示成功, 1 表示失败
ErrorMessage	String		具体的错误信息。当 Code 不为 0 时，ErrorMessage 会显示具体的错误

参数示例

{
    "AppId": "Your_AppId",
    "BusinessId": "Your_BusinessId",    
    "RoomId": "Your_RoomId",
    "TaskId": "Your_TaskId",
    "Code": 0,
    "ErrorMessage": "" 
}

json
房间相关
UserJoinRoom

EventType

UserJoinRoom

事件详情

可见用户进入房间。

相同 UserId 用户重复进房时，每一次进房都会触发该回调，建议进行去重操作。

注：关于可见用户和不可见用户，参看 setUserVisibility。

EventData

参数	类型	示例值	说明
RoomId	String	6992870232038591758	房间 ID
UserId	String	4398491447867063	用户 ID
DeviceType	String	android/ios/web/mac/windows	终端类型
Timestamp	Int64	1611736812853	该事件在 RTC 服务器上发生的 Unix 时间戳 (ms)
ExtraInfo	String	Your_ExtraInfo	客户端 SDK 通过 joinRoom 传入的用户附加信息。

参数示例

{
    "RoomId": "6992870232038591758",
    "UserId": "4398491447867063",
    "DeviceType": "android",
    "Timestamp": 1611736812853,
    "ExtraInfo":"Your_ExtraInfo"
}

json
UserLeaveRoom

EventType

UserLeaveRoom

事件详情

可见用户退出房间。

相同 UserId 用户反复登录造成被踢，被踢掉用户不会触发此回调。只有在最后一次退房时会触发该回调。

EventData

参数	类型	示例值	说明
RoomId	String	6992870232038591758	房间 ID
UserId	String	4398491447867063	用户 ID
DeviceType	String	android/ios/web/mac/windows	终端类型
Reason	String		原因。参看 Reason
Timestamp	Int64	1611736812853	该事件在 RTC 服务器上发生的 Unix 时间戳 (ms)
Duration	Int64	20	用户在房间里的通话时长(s)。

Reason:

值	含义
userLeave	正常退房
connectionLost	因断网、杀进程等异常原因离开房间。用户离开房间 30 s 后触发此回调。
kickedByAdmin	使用 OpenAPI 接口将某用户踢出房间
roomDismissByAdmin	使用 OpenAPI 接口解散房间，将房间内所有用户踢出房间
onUserTokenDidExpire	Token 过期被踢出房间
other	其他原因

参数示例

{
    "RoomId": "6992870232038591758",
    "UserId": "4398491447867063",
    "DeviceType": "android",
    "Reason": "userLeave",
    "Timestamp": 1611736812853,
    "Duration": 20
}

json
InvisibleUserJoinRoom

EventType

InvisibleUserJoinRoom

事件详情

不可见用户进入房间。

相同 UserId 用户重复进房时，每一次进房都会触发该回调，建议进行去重操作。

EventData

参数	类型	示例值	说明
RoomId	String	6992870232038591758	房间 ID
UserId	String	4398491447867063	用户 ID
DeviceType	String	android/ios/web/mac/windows	终端类型
Timestamp	Int64	1611736812853	该事件在 RTC 服务器上发生的 Unix 时间戳 (ms)
ExtraInfo	String	Your_ExtraInfo	客户端 SDK 通过 joinRoom 传入的用户附加信息。

参数示例

{
    "RoomId": "6992870232038591758",
    "UserId": "4398491447867063",
    "DeviceType": "android",
    "Timestamp": 1611736812853,
    "ExtraInfo":"Your_ExtraInfo"
}

json
InvisibleUserLeaveRoom

EventType

InvisibleUserLeaveRoom

事件详情

不可见用户退出房间。

相同 UserId 用户反复登录造成被踢，被踢掉用户不会触发此回调。只有在最后一次退房时会触发该回调。

EventData

参数	类型	示例值	说明
RoomId	String	6992870232038591758	房间 ID
UserId	String	4398491447867063	用户 ID
DeviceType	String	android/ios/web/mac/windows	终端类型
Reason	String		原因。参看 Reason
Timestamp	Int64	1611736812853	该事件在 RTC 服务器上发生的 Unix 时间戳 (ms)
Duration	Int64	20	用户在房间里的通话时长(s)。

Reason:

值	含义
userLeave	正常退房
connectionLost	因断网、杀进程等异常原因离开房间。用户离开房间 30 s 后触发此回调。
kickedByAdmin	使用 OpenAPI 接口将某用户踢出房间
roomDismissByAdmin	使用 OpenAPI 接口解散房间，将房间内所有用户踢出房间
onUserTokenDidExpire	Token 过期被踢出房间
other	其他原因

参数示例

{
    "RoomId": "6992870232038591758",
    "UserId": "4398491447867063",
    "DeviceType": "android",
    "Reason": "userLeave",
    "Timestamp": 1611736812853,
    "Duration": 20
}

json
RoleChangeInvisible2Visible

EventType

RoleChangeInvisible2Visible

事件详情

可见用户调用 setUserVisibility ，不可见 -> 可见。

EventData

参数	类型	示例值	说明
RoomId	String	6992870232038591758	房间 ID
UserId	String	4398491447867063	用户 ID
Timestamp	Int64	1611736812853	该事件在 RTC 服务器上发生的 Unix 时间戳 (ms)

参数示例

{
    "RoomId": "6992870232038591758",
    "UserId": "4398491447867063",
    "Timestamp": 1611736812853
}

json
RoleChangeVisible2Invisible

EventType

RoleChangeVisible2Invisible

事件详情

可见用户调用 setUserVisibility ，可见 -> 不可见。

EventData

参数	类型	示例值	说明
RoomId	String	6992870232038591758	房间 ID
UserId	String	4398491447867063	用户 ID
Timestamp	Int64	1611736812853	该事件在 RTC 服务器上发生的 Unix 时间戳 (ms)

参数示例

{
    "RoomId": "6992870232038591758",
    "UserId": "4398491447867063",
    "Timestamp": 1611736812853
}

json
RoomCreate

EventType

RoomCreate

事件详情

RTC 房间创建。

EventData

参数	类型	示例值	说明
RoomId	String	6992870232038591758	房间 ID
Timestamp	Int64	1611736812853	该事件在 RTC 服务器上发生的 Unix 时间戳 (ms)

参数示例

{
    "RoomId": "6992870232038591758",
    "Timestamp": 1611736812853
}

json
RoomDestroy

EventType

RoomDestroy

事件详情

RTC 房间销毁，房间中所有用户全部离开房间。

EventData

参数	类型	示例值	说明
RoomId	String	6992870232038591758	房间 ID
Timestamp	Int64	1611736812853	该事件在 RTC 服务器上发生的 Unix 时间戳 (ms)

参数示例

{
    "RoomId": "6992870232038591758",
    "Timestamp": 1611736812853
}

json
音频切片相关
SegmentRealTimeData(2020-12-01)

EventType

SegmentRealTimeData

事件详情

指定音频流切片结果。

EventData

参数名	类型	示例值	描述
AppId	String	Your_AppId	应用的唯一标志
BusinessId	String	Your_BusinessId	业务标识
RoomId	String	Your_RoomId	房间 ID，是房间的唯一标志
TaskId	String	Your_TaskId	切片任务 ID。
TosBucket	String	Your_TosBucket	储存切片的 tos bucket
Data	array of AudioData	/	
Identifier	String	Your_Identifier	切片任务标志。若未自定义切片名，切片名默认为 UUID，自定义切片名时为自定义名称。自定义切片文件名由 Identifier + UserId + 时间戳 + 序号组成。默认情况下 Identifier 为 随机生成的 UUID。在自定义文件名时，Identifier 的命名规则符合正则表达式：[a-zA-Z0-9_@-.]{1,128}。

AudioData

参数名	类型	示例值	描述
Stream	Stream	/	音频切片对应的流的信息。
ObjectKey	String	Your_ObjectKey	音频切片的文件名。
SampleRate	Int	44100	音频采样率，单位 Hz。
BitsPerSample	Int	16	每个音频采样的比特数。
Channels	Int	2	音频声道数。
Size	Uint64	256044	音频切片大小，单位为 byte。
TimeStamp	Int64	1647317680214	音频文件生成时对应的UNIX时间戳。单位为毫秒
Duration	Uint32	8000	音频的时长，单位为毫秒。

Stream

参数名称	类型	必填	示例值	描述
Index	Uint32	否	0	在自定义布局中，使用 Index 对流进行标志。后续在 Layout.regions.StreamIndex 中，你需要使用 Index 指定对应流的布局设置。
UserId	String	是	Your_UserId	用户Id，表示这个流所属的用户。
StreamType	Uint32	否	0	流的类型，值可以取0或1，默认值为0。0表示普通音视频流，1表示屏幕流。

参数示例

{
    "AppId": "Your_AppId",
    "BusinessId": "Your_BusinessId",
    "RoomId": "Your_RoomId",
    "TaskId": "Your_TaskId",
    "TosBucket": "Your_TosBucket",
    "Data": [
        {
            "Stream": {
                "UserId": "Your_UserId",
                "StreamType": 0
            },
            "ObjectKey": "Your_ObjectKey",
            "SampleRate": "44100",
            "BitsPerSample": "16",
            "Channels": "2",
            "Size": 256044,
            "TimeStamp": 1647317680214,
            "Duration": 8000
        }
    ], 
    "Identifier": "Your_Identifier" }

json
SegmentRealTimeData(2022-06-01)

EventType

SegmentRealTimeData

事件详情

指定音频流切片结果。

EventData

参数名	类型	示例值	描述
AppId	String	Your_AppId	应用的唯一标志
BusinessId	String	Your_BusinessId	业务标识
RoomId	String	Your_RoomId	房间 ID，是房间的唯一标志
TaskId	String	Your_TaskId	切片任务 ID。
Bucket	String	Your_Bucket	存储音频切片的桶名称
Data	array of AudioData		
Identifier	String	切片任务标志。若未自定义切片名，切片名默认为 UUID，自定义切片名时为自定义名称。自定义切片文件名由 Identifier + UserId + 时间戳 + 序号组成。默认情况下 Identifier 为 随机生成的 UUID。在自定义文件名时，Identifier 的命名规则符合正则表达式：[a-zA-Z0-9_@-.]{1,128}。

AudioData

参数名	类型	示例值	描述
Stream	Stream	/	音频切片对应的流的信息。
ObjectKey	String	Your_ObjectKey	音频切片的对象键。
SampleRate	Int	44100	音频采样率，单位 Hz。
BitsPerSample	Int	16	每个音频采样的比特数。
Channels	Int	2	音频声道数。
Size	Uint64	256044	音频切片大小，单位为 byte。
TimeStamp	Int64	1647317680214	音频文件生成时对应的UNIX时间戳。单位为毫秒
Duration	Uint32	8000	音频的时长，单位为毫秒。

Stream

参数名称	类型	必填	示例值	描述
Index	Uint32	否	0	在自定义布局中，使用 Index 对流进行标志。后续在 Layout.regions.StreamIndex 中，你需要使用 Index 指定对应流的布局设置。
UserId	String	是	Your_UserId	用户Id，表示这个流所属的用户。
StreamType	Uint32	否	0	流的类型，值可以取0或1，默认值为0。0表示普通音视频流，1表示屏幕流。

参数示例

{
    "AppId": "Your_AppId",
    "BusinessId": "Your_BusinessId",
    "RoomId": "Your_RoomId",
    "TaskId": "Your_TaskId",
    "Bucket": "Your_Bucket",
    "Data": [
        {
            "Stream": {
                "UserId": "Your_UserId",
                "StreamType": 0
            },
            "ObjectKey": "Your_ObjectKey",
            "SampleRate": "44100",
            "BitsPerSample": "16",
            "Channels": "2",
            "Size": 256044,
            "TimeStamp": 1647317680214,
            "Duration": 8000
        }
    ], 
    "Identifier": "Your_Identifier" 
    }

json
SegmentRealTimeData(2023-11-01)

EventType

SegmentRealTimeData

事件详情

指定音频流切片结果。

EventData

参数名	类型	示例值	描述
AppId	String	Your_AppId	应用的唯一标志
BusinessId	String	Your_BusinessId	业务标识
RoomId	String	Your_RoomId	房间 ID，是房间的唯一标志
TaskId	String	Your_TaskId	切片任务 ID。
Bucket	String	Your_Bucket	存储音频切片的桶名称
Data	array of AudioData		
Identifier	String	切片任务标志。若未自定义切片名，切片名默认为 UUID，自定义切片名时为自定义名称。自定义切片文件名由 Identifier + UserId + 时间戳 + 序号组成。默认情况下 Identifier 为 随机生成的 UUID。在自定义文件名时，Identifier 的命名规则符合正则表达式：[a-zA-Z0-9_@-.]{1,128}。

AudioData

参数名	类型	示例值	描述
Stream	Stream	/	音频切片对应的流的信息。
ObjectKey	String	Your_ObjectKey	音频切片的对象键。
SampleRate	Int	44100	音频采样率，单位 Hz。
BitsPerSample	Int	16	每个音频采样的比特数。
Channels	Int	2	音频声道数。
Size	Uint64	256044	音频切片大小，单位为 byte。
TimeStamp	Int64	1647317680214	音频文件生成时对应的UNIX时间戳。单位为毫秒
Duration	Uint32	8000	音频的时长，单位为毫秒。

Stream

参数名称	类型	必填	示例值	描述
Index	Uint32	否	0	在自定义布局中，使用 Index 对流进行标志。后续在 Layout.regions.StreamIndex 中，你需要使用 Index 指定对应流的布局设置。
UserId	String	是	Your_UserId	用户Id，表示这个流所属的用户。
StreamType	Uint32	否	0	流的类型，值可以取0或1，默认值为0。0表示普通音视频流，1表示屏幕流。

参数示例

{
    "AppId": "Your_AppId",
    "BusinessId": "Your_BusinessId",
    "RoomId": "Your_RoomId",
    "TaskId": "Your_TaskId",
    "Bucket": "Your_Bucket",
    "Data": [
        {
            "Stream": {
                "UserId": "Your_UserId",
                "StreamType": 0
            },
            "ObjectKey": "Your_ObjectKey",
            "SampleRate": "44100",
            "BitsPerSample": "16",
            "Channels": "2",
            "Size": 256044,
            "TimeStamp": 1647317680214,
            "Duration": 8000
        }
    ], 
    "Identifier": "Your_Identifier" 
    }

json
抽帧截图相关
SnapshotRealTimeData(2020-12-01)

EventType

SnapshotRealTimeData

事件详情

指定视频流截图结果。

EventData

参数名	类型	示例值	描述
AppId	String	Your_AppId	应用的唯一标志
BusinessId	String	Your_BusinessId	业务标识
RoomId	String	Your_RoomId	房间 ID，是房间的唯一标志
TaskId	String	Your_TaskId	截图任务 ID
TosBucket	String	Your_TosBucket	储存截图的 TOS bucket
Data	array of ImageData		

ImageData

参数名	类型	示例值	描述
Stream	Stream	/	截图对应的流的信息。
ObjectKey	String	Your_ObjectKey	截图对应的 TOS key。
Format	Uint32	0	截图格式。
0: JPEG
1: PNG

Width	Uint32	640	截图宽度，单位为像素。
Height	Uint32	360	截图高度，单位为像素。
Size	Uint64	28802	截图大小，单位为 byte。
TimeStamp	Uint64	1647316896085	截图生成时对应的 UNIX 时间戳。

Stream

参数名称	类型	必填	示例值	描述
Index	Uint32	否	0	在自定义布局中，使用 Index 对流进行标志。后续在 Layout.regions.StreamIndex 中，你需要使用 Index 指定对应流的布局设置。
UserId	String	是	Your_UserId	用户Id，表示这个流所属的用户。
StreamType	Uint32	否	0	流的类型，值可以取0或1，默认值为0。0表示普通音视频流，1表示屏幕流。

参数示例

{
    "AppId": "Your_AppId",
    "BusinessId": "Your_BusinessId",
    "RoomId": "Your_RoomId",
    "TaskId": "Your_TaskId",
    "TosBucket": "Your_TosBucket",
    "Data": [
        {
            "Stream": {
                "UserId": "Your_UserId",
                "StreamType": 0
            },
            "ObjectKey": "Your_ObjectKey",
            "Format": 0,
            "Width": 640,
            "Height": 360,
            "Size": 28802,
            "TimeStamp": 1647316896085
        }
    ]
}

json
SnapshotRealTimeData(2022-06-01)

EventType

SnapshotRealTimeData

事件详情

指定视频流截图结果。

EventData

参数名	类型	示例值	描述
AppId	String	Your_AppId	应用的唯一标志
BusinessId	String	Your_BusinessId	业务标识
RoomId	String	Your_RoomId	房间 ID，是房间的唯一标志
TaskId	String	Your_TaskId	截图任务 ID
Bucket	String	Your_Bucket	存储截图的桶名称。当存储平台为 TOS，或者支持 S3 协议的第三方存储平台时此字段才会生效。
VeImageXServiceId	String	/	VeImageX 的服务 ID。当存储平台为 VeImageX 时此字段才会生效。
Data	array of ImageData		

ImageData

参数名	类型	示例值	描述
Stream	Stream	/	截图对应的流的信息。
ObjectKey	String	Your_ObjectKey	对象键名称。当存储平台为 TOS，或者支持 S3 协议的第三方存储平台时此字段才会生效，你可以根据 ObjecetKey 找到对应的图片文件。
VeImageXUri	String	Your_VeImageXUri	VeImageX 的统一资源标识符。当存储平台为 VeImageX 时此字段才会生效，你可以根据 VeImageXUri找到对应的图片文件。
Format	Uint32	0	截图格式。
0: JPEG
1: PNG

Width	Uint32	640	截图宽度，单位为像素。
Height	Uint32	360	截图高度，单位为像素。
Size	Uint64	28802	截图大小，单位为 byte。
TimeStamp	Uint64	1647316896085	截图生成时对应的 UNIX 时间戳。

Stream

参数名称	类型	必填	示例值	描述
Index	Uint32	否	0	在自定义布局中，使用 Index 对流进行标志。后续在 Layout.regions.StreamIndex 中，你需要使用 Index 指定对应流的布局设置。
UserId	String	是	Your_UserId	用户Id，表示这个流所属的用户。
StreamType	Uint32	否	0	流的类型，值可以取0或1，默认值为0。0表示普通音视频流，1表示屏幕流。

参数示例

{
    "AppId": "Your_AppId",
    "BusinessId": "Your_BusinessId",
    "RoomId": "Your_RoomId",
    "TaskId": "Your_TaskId",
    "Bucket": "Your_Bucket",
    "Data": [
        {
            "Stream": {
                "UserId": "Your_UserId",
                "StreamType": 0
            },
            "ObjectKey": "Your_ObjectKey",
            "Format": 0,
            "Width": 640,
            "Height": 360,
            "Size": 28802,
            "TimeStamp": 1647316896085
        }
    ]
}

json
SnapshotRealTimeData(2023-11-01)

EventType

SnapshotRealTimeData

事件详情

指定视频流截图结果。

EventData

参数名	类型	示例值	描述
AppId	String	Your_AppId	应用的唯一标志
BusinessId	String	Your_BusinessId	业务标识
RoomId	String	Your_RoomId	房间 ID，是房间的唯一标志
TaskId	String	Your_TaskId	截图任务 ID
Bucket	String	Your_Bucket	存储截图的桶名称。当存储平台为 TOS，或者支持 S3 协议的第三方存储平台时此字段才会生效。
VeImageXServiceId	String	/	VeImageX 的服务 ID。当存储平台为 VeImageX 时此字段才会生效。
Data	array of ImageData		

ImageData

参数名	类型	示例值	描述
Stream	Stream	/	截图对应的流的信息。
ObjectKey	String	Your_ObjectKey	对象键名称。当存储平台为 TOS，或者支持 S3 协议的第三方存储平台时此字段才会生效，你可以根据 ObjecetKey 找到对应的图片文件。
VeImageXUri	String	Your_VeImageXUri	VeImageX 的统一资源标识符。当存储平台为 VeImageX 时此字段才会生效，你可以根据 VeImageXUri找到对应的图片文件。
Format	Uint32	0	截图格式。
0: JPEG
1: PNG

Width	Uint32	640	截图宽度，单位为像素。
Height	Uint32	360	截图高度，单位为像素。
Size	Uint64	28802	截图大小，单位为 byte。
TimeStamp	Uint64	1647316896085	截图生成时对应的 UNIX 时间戳。

Stream

参数名称	类型	必填	示例值	描述
Index	Uint32	否	0	在自定义布局中，使用 Index 对流进行标志。后续在 Layout.regions.StreamIndex 中，你需要使用 Index 指定对应流的布局设置。
UserId	String	是	Your_UserId	用户Id，表示这个流所属的用户。
StreamType	Uint32	否	0	流的类型，值可以取0或1，默认值为0。0表示普通音视频流，1表示屏幕流。

参数示例

{
    "AppId": "Your_AppId",
    "BusinessId": "Your_BusinessId",
    "RoomId": "Your_RoomId",
    "TaskId": "Your_TaskId",
    "Bucket": "Your_Bucket",
    "Data": [
        {
            "Stream": {
                "UserId": "Your_UserId",
                "StreamType": 0
            },
            "ObjectKey": "Your_ObjectKey",
            "Format": 0,
            "Width": 640,
            "Height": 360,
            "Size": 28802,
            "TimeStamp": 1647316896085
        }
    ]
}

json
在线媒体流相关
RelayStreamStateChanged

EventType

RelayStreamStateChanged

事件详情

输入在线媒体流状态发生变化。

EventData

参数名	类型	示例值	描述
RoomId	String	Your_RoomId	房间 ID，是房间的唯一标志
TaskId	String	Your_TaskId	任务 ID
UserId	String	Your_UserId	在线媒体流对应的的 UserId
StreamUrl	String	rtmp://xxx	在线流媒体地址
Status	Int	1	任务状态：
1：待机中
2：连接中
3：运行中
4：已停止
5：重试中。
StartTimeStamp	Int	0	任务起始时间戳，用于定时播放，Unix时间，单位为秒。默认为 0，表示立即启动。
Msg	String	/	描述信息
Reason	Int	1	任务停止原因：
1：空闲超时
2：停止接口调用
3：流播放结束
4：内部错误
5：url地址异常
6：编码格式不支持
7：token 错误
8：没有发布权限
9：被移除房间
仅当 status=4 时，Reason 有值。

参数示例

{
        "AppId": "Your_AppId",
        "EventId": "Your_eventId",
        "EventTime": "2021-08-17T19:22:02+08:00",
        "EventType": "RelayStreamStateChanged",
        "EventData": {
                "RoomId": "Your_RoomId",
                "TaskId": "Your_TaskId",  
                "UserId": "Your_UserId",    
                "StreamUrl": "rtmp://xxx",        
                "Status": 1,
                "StartTimeStamp": 0,       
                "Msg": "",
                "Vid": "xxxxvvv"，
                "Reason": 4
        },
}

json
云录屏相关
Webcast

EventType

Webcast

事件详情

云录屏任务状态发生变化

EventData

参数名	类型	示例值	描述
AppId	String	Your_AppId	应用的唯一标志
RoomId	String	Your_RoomId	房间 ID，是房间的唯一标志
BusinessId	String	Your_BusinessId	业务标识
TaskId	String	Your_TaskId	任务 ID
Status	Int	1	任务状态：
1：开始转推
2：正常结束
3：异常结束
4：页面刷新
5：任务重调度（推流用户重新进房）
Reason	String	/	异常结束描述信息，当 Status = {3,4,5} 时才有值，枚举值为 {：AudioCaptureModuleError、 WebRenderModuleError、SourceURLInaccessible、StartEventTimeout、PageBlank、PageCrash、PageFreeze} 。

参数示例

{
    "AppId": "Your_AppId",
    "BusinessId": "Your_BusinessId",
    "RoomId": "Your_RoomId",
    "TaskId": "Your_TaskId",
    "Status": 1
}

json
转推直播相关
TranscodeStarted

EventType

TranscodeStarted

事件详情

通过调用 RTC 服务端 OpenAPI 或客户端 SDK 的 API 发起的转推直播任务开始。

不同版本转推直播功能在转推直播任务开始时均触发此回调。

EventData

参数名	类型	示例值	描述
AppId	String	Your_AppId	应用的唯一标志
RoomId	String	Your_RoomId	房间 ID，是房间的唯一标志
TaskId	String	Your_TaskId	转推直播任务 ID。通过服务端发起时，该值为调用 OpenAPI 时传入的 TaskId。通过客户端 SDK 发起时，TaskId 是按照 userId@@taskId 格式拼接而成的字符串；当传入的 taskId 为空时，这里的 TaskId 为 userId。
Timestamp	Int64	1661150097044	该事件在 RTC 服务器上发生的 Unix 时间戳 (ms)
PushURL	String	rtmp://xxxx	推流 CDN 地址。该参数当前仅在合流转推下返回。
Code	Int	0	操作结果类型。
0： 创建任务成功。
1：创建任务失败。

ErrorMessage	String		具体错误信息，当 Code为1时，ErrorMessage 会显示具体的错误信息

参数示例

{
 	"AppId": "Your_AppID",  
    "RoomId": "Your_RoomID",
    "TaskId": "Your_TaskID",
    "Timestamp": 1661150097044,
    "PushURL": "rtmp://xxxx",    
    "Code": 0,
    "ErrorMessage": ""
}

json
TranscodeStopped

EventType

TranscodeStopped

事件详情

通过调用 RTC 服务端 OpenAPI 或客户端 SDK 的 API 终止了转推直播任务。
不同版本转推直播功能在转推直播任务结束时均触发此回调。

EventData

参数名	类型	示例值	描述
AppId	String	Your_AppId	应用的唯一标志
RoomId	String	Your_RoomId	房间 ID，是房间的唯一标志
TaskId	String	Your_TaskId	转推直播任务 ID。通过服务端发起时，该值为调用 OpenAPI 时传入的 TaskId。通过客户端 SDK 发起时， TaskId 是按照 userId@@taskId 格式拼接而成的字符串；当传入的 taskId 为空时，这里的 TaskId 为 userId。
Timestamp	Int64	1661150097044	该事件在 RTC 服务器上发生的 Unix 时间戳 (ms)
PushURL	String	rtmp://xxxx	推流 CDN 地址。该参数当前仅在合流转推下返回。
Code	Int	0	操作结果类型。
0： 停止任务成功。
1：停止任务失败。

ErrorMessage	String		具体错误信息，当 Code为1时，ErrorMessage 会显示具体的错误信息

参数示例

{
 	"AppId":"Your_AppID",
    "RoomId":"Your_RoomID",
    "TaskId":"Your_TaskID",
    "Timestamp":1661150097044,
    "PushURL": "rtmp://xxxx",    
    "Code":0,
    "ErrorMessage":""
}

json
TranscodeUpdated

EventType

TranscodeUpdated

事件详情

通过调用 RTC 服务端 OpenAPI 或客户端 SDK 的 API 更新了合流转推任务。

不同版本转推直播功能在转推直播任务更新时均触发此回调。

EventData

参数名	类型	示例值	描述
AppId	String	Your_AppId	应用的唯一标志
RoomId	String	Your_RoomId	房间 ID，是房间的唯一标志
TaskId	String	Your_TaskId	转推直播任务 ID。通过服务端发起时，该值为调用 OpenAPI 时传入的 TaskId。通过客户端 SDK 发起时， TaskId 是按照 userId@@taskId 格式拼接而成的字符串；当传入的 taskId 为空时，这里的 TaskId 为 userId。
Timestamp	Int64	1661150097044	该事件在 RTC 服务器上发生的 Unix 时间戳 (ms)
PushURL	String	rtmp://xxxx	推流 CDN 地址。
Code	Int	0	操作结果类型。
0： 更新配置成功。
1：创建配置失败。

ErrorMessage	String		具体错误信息，当 Code为1时，ErrorMessage 会显示具体的错误信息

参数示例

{
    "AppId": "Your_AppID",
    "RoomId": "Your_RoomID", 
    "TaskId": "Your_TaskID",  
    "Timestamp": 1661150055041,   
    "PushURL": "rtmp://xxxx", 
    "Code": 0,
    "ErrorMessage": ""
}

json
TranscodeStateChanged

EventType

TranscodeStateChanged

事件详情

转推直播任务状态变化通知。

不同版本转推直播功能在转推直播任务状态变化时均触发此回调。

状态变化包括用户主动调用 RTC 服务端 OpenAPI 或客户端 SDK 的 API 更新了合流转推任务参数。也包括任务执行过程中出现的状态流转。

EventData

参数名	类型	示例值	描述
AppId	String	Your_AppId	应用的唯一标志
RoomId	String	Your_RoomId	房间 ID，是房间的唯一标志
TaskId	String	Your_TaskId	转推直播任务 ID。通过服务端发起时，该值为调用 OpenAPI 时传入的 TaskId。通过客户端 SDK 发起时， TaskId 是按照 userId@@taskId 格式拼接而成的字符串；当传入的 taskId 为空时，这里的 TaskId 为 userId。
Timestamp	Int64	1661150097044	该事件在 RTC 服务器上发生的 Unix 时间戳 (ms)
StartTime	Int64	1661150054990	转推直播任务创建的 Unix 时间戳（ms）
FinishTime	Int64	0	转推直播任务结束的 Unix 时间戳（ms）
PushURL	String	rtmp://xxxx	推流 CDN 地址。该参数当前仅在合流转推下返回。
TaskState	Int	2	任务状态，枚举值为 {1，2，3，4}。
1：尚未开始推流或已停止推流。
2：正在重连 CDN 服务。
3：正在推流
4：当前推流地址已切换。此时 ErrorMessage 字段中会显示切换前后的推流地址。

Code	Int	0	错误码。枚举值为 {0，1，2，3，4，5，9999}。
0：请求处理成功。
1：请求参数校验发生错误。
2：订阅 RTC 用户发布的音视频流发生错误。
3：合流任务执行过程发生错误。
4：推流到 CDN 发生错误。
5：RTC 房间内没有合流任务需要订阅的音视频流。
9999：其他系统异常。

ErrorMessage	String	-	具体错误信息。
OnStatusCode	String	-	推流失败时 CDN 返回的错误码。
OnStatusDescription	String	-	推流失败时 CDN 返回的错误详情。

参数示例

{
    "AppId": "Your_AppID",
    "RoomId": "Your_RoomID", 
    "TaskId": "Your_TaskID",  
    "Timestamp": 1661150097044, 
    "StartTime": 1661150054990,
    "FinishTime": 0,   
    "PushURL": "rtmp://xxxx", 
    "TaskState": 3,    
    "Code": 0,
    "ErrorMessage": "",
    "OnStatusCode": "",
    "OnStatusDescription": ""
}

json
公共流相关
PushPublicStream

EventType
PushPublicStream

事件详情

公共流推送异常或推送结束

EventData

参数名	类型	示例值	描述
AppId	String	Your_AppId	应用的唯一标志
BusinessId	String	Your_BusinessId	业务标识
PublicStreamId	String	Your_PublicStreamId	公共流对应的流 Id
Timestamp	Int64	1661150097044	该事件在 RTC 服务器上发生的 Unix 时间戳 (ms)
Status	Int	2	公共流任务状态。枚举值为 {1，2，3}。
1：公共流启动成功
2：公共流推送结束
3：公共流推送异常

Msg	String		具体的错误信息
Code	Int	2	错误码。枚举值为 {1，2，3}。
1：空闲超时结束。
2：OpenAPI 调用结束。
3：内部错误结束。

参数示例

{
    "AppId":"Your_AppID", 
    "BusinessId":"Your_BusinessId",    
    "PublicStreamId":"Your_PublicStreamId", 
    "Timestamp":1661150097044, 
    "Status":2, 
    "Msg":"call api stop",     
    "Code":2
}

json
歌曲查询相关
MusicRemoved

EventType
MusicRemoved

事件详情

曲库下架歌曲列表

EventData

参数名	类型	示例值	描述
AppId	String	Your_AppId	应用的唯一标志
Timestamp	Int	1611736812853	该事件在 RTC 服务器上发生的时间戳，Unix 时间，单位为毫秒
SongUpdate	Array of SongUpdate	/	下架歌曲详细信息

SongList

参数名	类型	示例值	描述
SongId	String	Your_SongId	歌曲 ID
VendorId	Int	1	供应商 ID
UpdateAt	Int	1611736812853	歌曲更新时间戳，Unix 时间，单位为毫秒

参数示例

{
    "AppId": "Your_AppId",
    "Timestamp": 1611736812853,
    "SongUpdate": [
        {
            "SongId": "Your_SongId",
            "VendorId": 2,
            "UpdateAt": 1611736812853
        }
    ]
}

json
互动白板文件转码
WbTranscodeProgressChanged

EventType
WbTranscodeProgressChanged

事件详情

文件转码进度。触发时机说明如下：

任务开始时
任务完成时
静态转码任务每完成 10 页时，也会触发一次回调。

EventData

参数名	类型	示例值	描述
TaskId	String	5f848de8-4258-4c41-964b-80556552ae82	任务唯一标识


TranscodeMode

	

Int

	

0

	

0：静态转码
1：动态转码


Progress	Int	0	进度：当前页数/总页数 *100%。对于动态转码任务，取值为 0 或 100。
FileName	String	demo	源文件文件名
TotalPages	Int	5	总页数。对静态转码任务有效。
WbTranscodeFinished

EventType

WbTranscodeFinished

事件详情
文件转码任务结束后回调一次，成功或失败。

EventData

参数名	类型	示例值	描述
TaskId	String	5f848de8-4258-4c41-964b-80556552ae82	任务唯一标识


TranscodeMode

	

Int

	

0

	

0：静态转码
1：动态转码


FileName	String	demo	源文件文件名
FileId	String	001	动态转码文件ID。对动态转码任务有效。
TotalPages	Int	5	总页数。
Width	Int	640	分辨率宽，单位：像素。对静态转码任务有效。
Height	Int	360	分辨率高，单位：像素。对静态转码任务有效。
ThumbnailWidth	int	64	缩略图分辨率宽，单位：像素
ThumbnailHeight	int	64	缩略图分辨率高，单位：像素


Images

	

Array of Image

	

None

	

转码图片结果

静态转码缩略图 URL。
动态转码指定缩略图时，返回缩略图 URL。不指定则为空

ErrCodeN	Int	400	错误码编号，含义见白板错误码
ErrCode	String	InvalidParameter	错误码类型，含义见白板错误码
ErrMsg	String	和错误参数有关的信息	错误码信息，含义见白板错误码

Image
参数名	类型	示例值	描述
PageId	Int	1	页数，从1开始编号
Img	String	https://example.com/demo.png	转码图片URL
ThumbnailUrl	String	https://example.com/thumbnail.png	转码缩略图URL，如没有指定缩略图，则为空
实时对话式 AI
VoiceChat

EventType

VoiceChat

事件详情

智能体任务状态变化。

EventData

参数名	类型	示例值	描述
AppId	String	661e****543cf	音视频应用的唯一标识。
BusinessId	String	biz1	业务标识。
RoomId	String	room1	房间 ID，房间的唯一标识。
TaskId	String	task1	智能体任务 ID。
UserID	String	user1	说话人 UserId。
RoundID	Int64	0	对话轮次。从 0 开始计数。
EventTime	Int64	1611736812853	该事件在 RTC 服务器上发生的 Unix 时间戳 (ms)。
EventType	Int64	0	任务状态类型。
0：智能体任务状态发生变化。
1：智能体任务出现错误。

RunStage	String	preParamCheck	状态详情。
taskStart：任务开始。
taskStop：任务结束。
beginAsking：房间用户开始说话。
asrFinish：房间用户结束说话。
answerFinish：智能体说话完成。
asr：ASR 处理阶段。
llm：LLM 处理阶段。
tts：TTS 处理阶段。
preParamCheck：参数校验错误。

ErrorInfo	ErrorInfo	-	任务错误详细信息。

ErrorInfo

参数名	类型	示例值	描述
Errorcode	Int	1001	错误状态码。
Reason	String	-	错误详细原因。

参数示例

{
    "AppId": "661e****543cf",
    "BusinessId": "biz1",
    "RoomId": "room1",
    "TaskId": "room1",
    "UserID": "user1",
    "RoundID": 0,
    "EventTime": 1611736812853,
    "EventType": 0,
    "RunStage": "taskStart",
    "ErrorInfo": {
        "Errorcode": 1001,
        "Reason": ""
    }
}
