Title: 公共错误码--实时音视频-火山引擎

URL Source: https://www.volcengine.com/docs/6348/70426

Markdown Content:
公共错误码--实时音视频-火山引擎


*   文档首页
/实时音视频/服务端 API 参考/调用方法/公共错误码

公共错误码

最近更新时间：2025.03.03 17:06:17 首次发布时间：2021.07.18 14:10:40

[我的收藏](https://www.volcengine.com/docs/favorite)

有用

有用

无用

无用

![Image 1](blob:http://localhost/7511cd80c66a21d3576513d5323b9f98)文档反馈

![Image 2](blob:http://localhost/8791860fec2e7731d895208f5ceb1ced)问问助手

如果调用 OpenAPI 失败，你会在相应结果中看到错误码和错误信息。以下是错误码，及对应的说明和处理方法：

| 错误码 | 说明 | 处理方法 |
| --- | --- | --- |
| `InvalidParameter.TaskAlreadyExists` | 任务已存在 | 请勿重复创建 |
| `InvalidParameter.StartTimeAfterEndTime` | 查询开始时间晚于结束时间 | 调整请求参数 |
| `InvalidParameter.OutofTimeRange` | 查询要求的时间范围长度超出限制 | 缩短查询的时间范围 |
| `InvalidParameter.ErrorActiveTaskNotFound` | 指定任务不存在或已经停止 | 请输入正确的 TaskId |
| `InvalidParameter.InvalidRtmpAddress` | 推流地址不合法 | 请确认推流地址 |
| `InvalidParameter.IllegalRoomID` | `RoomId` 不合法 | `RoomId` 格式错误，请填写正确房间 ID |
| `InvalidParameter.BodyIsEmpty` | 请求内容为空 | 请填写请求参数 |
| `InvalidParameter.Unmarshal` | 请求解析失败 | 请填写正确的参数格式 |
| `InvalidParameter.EmptyAccountId` | `AccountId` 为空 | 请填写 `AccountId` |
| `InvalidParameter.NotExistAppId` | `AppID` 不存在 | 请检查 `AppID` |
| `InvalidParameter.IllegalAppId` | `AppID` 不合法 | 请检查 `AppID` |
| `InvalidParameter.IllegalTo` | 接收用户名不合法 | 请检查接收用户参数 |
| `InvalidParameter.IllegalFrom` | 发送用户名不合法 | 请检查发送用户名参数 |
| `InvalidParameter.SizeOverLimit` | 消息大小超过限制 | 请减小消息内容大小 |
| `InvalidParameter.UserCountOverLimit` | 接收用户数超过限制 | 请减少接收用户数 |
| `InvalidParameter` | 参数错误 | 请根据具体提示调整参数 如具体错误信息为 `page and resolution with height should be even`，请将分辨率参数调整为偶数 |
| `FlowLimitExceeded.TaskNumberOverLimit` | 任务数量超过限制 | 目前没有对任务数量进行限制 |
| `FlowLimitExceeded.QPSOverLimit` | qps 超过限制 | 请勿太频繁地发起请求 |
| `AccessDenied.RecordNotEnabled` | 录制功能未开通 | 请在[控制台](https://console.volcengine.com/rtc/cloudRTC?from=doc)上开通录制 |
| `AccessDenied.NoPermissionForApp` | 此 `AccountId` 没有操作该 `AppId` 的权限 | 请更换正确的 `AccountId` |
| `AccessDenied.ServiceNotEnabled` | 需要服务未开通 | 请在[控制台](https://console.volcengine.com/rtc/cloudRTC?from=doc)开通需要服务 |
| `InternalError.GenerateTokenFailed` | 生成`token`失败 | 详情请联系技术支持 |
| `InternalError.GetAutoTriggerPolicyFailed` | 获取自动录制配置失败 | 详情请联系技术支持 |
| `InternalError.GetRecordPolicyFailed` | 获取录制参数配置失败 | 详情请联系技术支持 |
| `InternalError.DBCheckTaskExistFailed` | 判断数据库中是否存在此任务失败 | 详情请联系技术支持 |
| `InternalError.DBCreateTaskFailed` | 数据库中创建任务失败 | 详情请联系技术支持 |
| `InternalError.DBReadTaskFailed` | 数据库中读取任务信息失败 | 详情请联系技术支持 |
| `InternalError.DBUpdateTaskFailed` | 数据库中更新任务信息失败 | 详情请联系技术支持 |
| `InternalError.UnknownInternalError` | 未知的内部错误 | 详情请联系技术支持 |
| `InternalError.TaskRepeat` | 未知的内部错误 | 请不要重复请求，或更换 `taskID` |
| `InternalError.Unknown` | 系统故障，未启动成功 | 详情请联系技术支持 |
| `InternalError.DB` | 服务端数据库查询失败 | 详情请联系技术支持 |
| `InternalError.Executor` | 服务端停止任务失败 | 请重试 |
| `InternalError.ReachRateLimit` | 触发系统限流 | 请勿太频繁地发起请求 |
| `InternalError.Parallel` | 并发操作同一个任务 | 请勿并发请求 |
| `InternalError.RPC` | `rpc` 调用失败 | 请重新发起请求 |
| `InternalError.NotFound` | 请重新发起请求 | 请重新发起请求 |
| `InternalError.UserNotFound` | 找不到接收用户 | 若确定用户已进房，请重新发起请求 |
| `InternalError.RoomNotFound` | 找不到房间 | 若确定房间已存在，请重新发起请求 |
| `InternalError.Marshal` | 序列化失败 | 请重新发起请求 |
| `InternalError.SendToEdge` | 推送给 `RTM Edge` 失败 | 请重新发起请求 |
| `InternalError.Redis` | 读写 `Redis` 失败 | 请重新发起请求 |
| `InternalError.TaskNotReady` | 任务已提交，还未开始运行 | 详情请联系技术支持 |
| `InternalError.InvalidMeta` | 任务内部参数不合法 | 详情请联系技术支持 |
| `InternalError.TaskControlError` | 处理任务出现错误 | 详情请联系技术支持 |
| `InternalError.NotifyConnectionRefused` | 内部连接被拒绝 | 详情请联系技术支持 |
| `InternalError.LockContention` | 锁竞争超时 | 详情请联系技术支持 |
| `InternalError.ServerError` | 服务器错误 | 详情请联系技术支持 |
| `InternalError.RPC` | 此 `AccountId` 没有操作该 `AppId` 的权限 |  |
| `InternalError.Unmarshal` | 请求解析失败 | 请填写正确的参数格式 |
| `ErrorParameter.MaxRecordSecond` | `MaxRecordSecond`参数错误 | 请填写正确的值 |
| `ErrorParameter.AppId` | `AppId` 错误 | 请确认 `AppId` 是否填写正确 |
| `MissingParameter.NoAppId` | 未填写 `AppId` | 请填写 `AppId` |
| `MissingParameter.NoAccount` | 账号 ID 未填写 | 请填写账号 ID |
| `MissingParameter.NoAction` | `Action` 缺失 | `Action` 未填写，请填写正确接口名称 |
| `MissingParameter.NoVersion` | `Version` 缺失 | `version` 未填写，请填写正确接口版本 |
| `MissingParameter.NoRoomId` | `RoomId` 缺失 | 请填写 `RoomId` |
| `MissingParameter.NoUserId` | `UserId` 缺失 | 请填写 `UserId` |
| `MissingParameter.NoToken` | `Token` 缺失 | 请填写 `Token` |
| `MissingParameter.NoAppId` | `AppId` 缺失 | 请填写 `AppId` |
| `MissingParameter.Forbidden` | 封禁时间不在有效范围内。 | 请填写有效封禁时间 |
| `Unauthorized` | 操作的 `AppId` 与账号 ID 不匹配 | 请确认 `AppId` 和账号 ID 是否正确 |
| `UnknownVersion` | `Version`未知 | 请将 `version` 重置为 `2020-12-01` |
| `UnknownAction` | `Action`未知 | 请 检查 `Action` 是否为 Open API 定义的 `Action` |
| `PartialSuccess` | 部分用户收到消息 | 如确定用户均在房间内，请重新发起请求 |
| `UpdateError.UpdateSingleStreamRecordNotSupported` | 单流录制不支持更新 | 当前单流录制不支持更新，请勿发起单流录制更新请求 |
| `UpdateError.CurrentLayoutNotSupported` | 不支持更新当前布局模式下的合流录制任务 | 请检查你设置的布局模式，仅自定义布局模式支持更新合流录制任务 |
| `UpdateError.UpdatePushSingleStreamNotSupported` | 单流转推不支持更新 | 当前单流转推不支持更新，请勿发起单流转推更新请求 |
