Title: 语音合成配置--实时音视频-火山引擎

URL Source: https://www.volcengine.com/docs/6348/1581713

Markdown Content:
语音合成配置--实时音视频-火山引擎

===============


在实时对话式 AI 场景中，你需要配置语音合成（TTS）能力，将大模型生成的文本转换为自然流畅的语音输出，实现智能体与真人用户的语音交互。RTC 提供一站式接入方案，只需在 `StartVoiceChat` 接口配置 `TTSConfig` 即可完成语音合成能力接入。本文将详细介绍不同服务的配置参数、注意事项及适用场景，帮助你根据需求选择合适的方案。

选择语音合成服务
--------

火山引擎提供以下 6 种语音合成接入方案，分别适用于不同场景：

*   **火山引擎语音合成**：生成速度快，满足常规语音播报需求。
*   **火山引擎语音合成大模型（非流式输入流式输出）**：支持 SSML 标记语言，相较于传统语音合成技术更自然。
*   **火山引擎语音合成大模型（流式输入流式输出）**：支持流式逐字级别输入级输出，进一步降低语音交互时延，且支持 Markdown 标记过滤、公式播报 和 Latex 能力。
*   **火山引擎声音复刻大模型（非流式输入流式输出）**：支持复刻真人音色，非流式文本输入、流式语音输出，语音合成速度快。
*   **火山引擎声音复刻大模型（流式输入流式输出）**：支持复刻真人音色，流式文本输入、流式语音输出，语音合成效果更自然。
*   **MiniMax 语音合成**：MiniMax 语音合成平台，支持多语言、混合音色和特殊发音标注。

火山语音合成
------

火山引擎语音合成采用端到端合成方案，生成速度快，满足常规语音播报需求，适合短语或标准回复，例如，提醒、系统反馈、数字播报。详细功能特性参看[语音合成](https://www.volcengine.com/docs/6561/79817)。

### 核心参数

使用 `StartVoiceChat` 接口配置火山引擎语音合成时，需通过 `Config.TTSConfig` 结构设置参数，以下为核心配置参数说明：

> 完整参数及说明可参看 [StartVoiceChat 接口文档](https://www.volcengine.com/docs/6348/1558163)。

| 参数名 | 类型 | 是否必填 | 示例值 | 说明 |
| --- | --- | --- | --- | --- |
| `IgnoreBracketText` | Int[] | 否 | `[1,2]` | 过滤大模型返回内容中指定标点符号中的文字后再进行语音合成。 |
| `Provider` | String | 是 | `volcano` | 语音合成服务提供商，固定取值 `volcano`。 |
| `ProviderParams.app.appid` | String | 是 | `94****11` | 开通火山引擎语音合成服务后获取的 App ID，用于标识应用。你可登录[豆包语音控制台](https://console.volcengine.com/speech/service/8?)获取。 |
| `ProviderParams.app.cluster` | String | 是 | `volcano_tts` | 已开通语音合成服务对应的集群标识（Cluster ID）。你可登录[豆包语音控制台](https://console.volcengine.com/speech/service/8?)开通服务后获取。 |
| `ProviderParams.audio.voice_type` | String | 是 | `BV001_streaming` | 已开通音色对应的音色种类（Voice_type）。你可登录[豆包语音控制台](https://console.volcengine.com/speech/service/8?)购买音色后获取。。 |
| `ProviderParams.audio.speed_ratio` | Float | 否 | `1.0` | 语速。取值 `[0.2,3]`，默认 `1.0`，值越大语速越快。 |
| `ProviderParams.audio.volume_ratio` | Float | 否 | `1.0` | 音量。取值 `[0.1,3]`，默认 `1.0`，值越大音量越高。 |
| `ProviderParams.audio.pitch_ratio` | Float | 否 | `1.0` | 音高。取值 `[0.1,3]`，默认 `1.0`，值越大音调越高。 |

### 请求示例

你可参看以下示例，使用火山引擎语音合成：

```json
{
    "TTSConfig": {
        "IgnoreBracketText":[
            1,
            2
        ],
        "Provider": "volcano",
        "ProviderParams": {
            "app": {
                "appid": "94****11",
                "cluster": "volcano_tts"
            },
            "audio": {
                "voice_type": "BV001_streaming",
                "speed_ratio": 1.2,
                "volume_ratio": 1.1,
                "pitch_ratio": 1.0
            }
        }
    }
}
```

json

火山语音合成大模型（非流式输入流式输出）
--------------------

该方案基于大模型实现非流式文本输入、流式语音输出，支持 SSML 标记语言，相较于传统语音合成技术，大语音模型在口语自然度、连贯性、拟人度、音质、韵律、气口、情感、语气词表达等各方面更强。详细功能特性参看[大模型语音合成](https://www.volcengine.com/docs/6561/1257543#%E5%8A%9F%E8%83%BD%E7%89%B9%E6%80%A7)。

### 核心参数

> 完整参数及说明可参看 [StartVoiceChat 接口文档](https://www.volcengine.com/docs/6348/1558163)。

| 参数名 | 类型 | 是否必填 | 示例值 | 说明 |
| --- | --- | --- | --- | --- |
| `IgnoreBracketText` | Int[] | 否 | `[1,2]` | 过滤大模型返回内容中指定标点符号中的文字后再进行语音合成。 |
| `Provider` | String | 是 | `volcano` | 语音合成服务提供商，固定取值 `volcano`。 |
| `ProviderParams.app.appid` | String | 是 | `94****11` | 开通火山引擎语音合成大模型服务后获取的 App ID，用于标识应用。你可登录[豆包语音控制台](https://console.volcengine.com/speech/service/10007?)获取。 |
| `ProviderParams.app.cluster` | String | 是 | `volcano_tts` | 已开通语音合成大模型服务对应的集群标识（Cluster ID）。你可登录[豆包语音控制台](https://console.volcengine.com/speech/service/10007?)开通服务后获取。 |
| `ProviderParams.audio.voice_type` | String | 是 | `zh_female_meilinvyou_moon_bigtts` | 已开通音色对应的音色种类（Voice_type）。你可登录[豆包语音控制台](https://console.volcengine.com/speech/service/10007?)购买音色后获取。 |
| `ProviderParams.audio.pitch_rate` | Integer | 否 | `0` | 音调。取值 `[-12,12]`，默认 `0`，值越大音调越高。 |
| `ProviderParams.audio.speech_rate` | Integer | 否 | `0` | 语速。取值 `[-50,100]`，默认 `0`，`100` 为 2 倍速，`-50` 为 0.5 倍速。 |

### 请求示例

你可参看以下示例，使用火山引擎语音合成大模型（非流式输入流式输出）进行语音合成：

```json
{
    "TTSConfig": {
        "IgnoreBracketText":[
            1,
            2
        ],
        "Provider": "volcano",
        "ProviderParams": {
            "app": {
                "appid": "94****11",
                "cluster": "volcano_tts"
            },
            "audio": {
                "voice_type": "zh_female_meilinvyou_moon_bigtts",
                "pitch_rate": 2,
                "speech_rate": 30
            }
        }
    }
}
```

json

火山语音合成大模型（流式输入流式输出）
-------------------

该方案基于大模型支持流式文本输入和流式语音输出，支持流式逐字级别输入级输出，进一步降低基于大模型的语音交互时延，用户体感延迟低，且支持 Markdown 标记过滤、公式播报 和 Latex 能力。详细功能特性参看[大模型语音合成](https://www.volcengine.com/docs/6561/1257543#%E5%8A%9F%E8%83%BD%E7%89%B9%E6%80%A7)。

### 核心参数

> 完整参数及说明可参看 [StartVoiceChat 接口文档](https://www.volcengine.com/docs/6348/1558163)。

| 参数名 | 类型 | 是否必填 | 示例值 | 说明 |
| --- | --- | --- | --- | --- |
| `IgnoreBracketText` | Int[] | 否 | `[1,2]` | 过滤大模型返回内容中指定标点符号中的文字后再进行语音合成。 |
| `Provider` | String | 是 | `volcano_bidirection` | 语音合成服务提供商，固定取值 `volcano_bidirection`。 |
| `ProviderParams.app.appid` | String | 是 | `94****11` | 开通火山引擎语音合成大模型服务后获取的 App ID，用于标识应用。你可登录[豆包语音控制台](https://console.volcengine.com/speech/service/8?)获取。 |
| `ProviderParams.app.token` | String | 是 | `OaO****ws1` | 与语音合成大模型服务 App ID 对应的 AccessToken，用于身份认证。你可登录[豆包语音控制台](https://console.volcengine.com/speech/service/10011?)获取。Access Token 查找方式，可参看[如何获取 Token](https://www.volcengine.com/docs/6561/196768#q1%EF%BC%9A%E5%93%AA%E9%87%8C%E5%8F%AF%E4%BB%A5%E8%8E%B7%E5%8F%96%E5%88%B0%E4%BB%A5%E4%B8%8B%E5%8F%82%E6%95%B0appid%EF%BC%8Ccluster%EF%BC%8Ctoken%EF%BC%8Cauthorization-type%EF%BC%8Csecret-key-%EF%BC%9F)。 |
| `ProviderParams.audio.voice_type` | String | 是 | `BV001_streaming` | 已开通音色对应的音色种类（Voice_type）。你可登录[豆包语音控制台](https://console.volcengine.com/speech/service/10011?)购买音色后获取。 |
| `ProviderParams.audio.pitch_rate` | Integer | 否 | `0` | 音调。取值范围为 `[-12,12]`。默认值为 `0`。取值越大，音调越高。 |
| `ProviderParams.audio.speech_rate` | Integer | 否 | `0` | 语速。取值范围为`[-50,100]`，`100` 代表 2.0 倍速，`-50` 代表 0.5 倍速。默认值为 `0`。取值越大，语速越快。 |
| `ProviderParams.Additions.disable_markdown_filter` | Boolean | 否 | `true` | 是否过滤 Markdown 标记。 |
| `ProviderParams.Additions.enable_latex_tn` | Boolean | 否 | `true` | 是否播报 Latex 公式（需 `disable_markdown_filter` 为 `true` 生效）。 |
| `ProviderParams.ResourceId` | String | 是 | `volc.service_type.10029` | 调用服务的资源信息 ID，该参数固定取值：`volc.service_type.10029`。 |

### 请求示例

你可参看以下示例，使用火山语音合成大模型（流式输入流式输出）进行语音合成：

```json
{
    "TTSConfig": {
        "IgnoreBracketText":[
            1,
            2
        ],
        "Provider": "volcano_bidirection",
        "ProviderParams": {
            "app": {
                "appid": "94****11",
                "token": "OaO****ws1",
            },
            "audio": {
                "voice_type": "BV001_streaming",
                "pitch_rate": 1,
                "speech_rate": 20
            },
            "Additions": {
                "enable_latex_tn": true,
                "disable_markdown_filter": true
            },
            "ResourceId": "volc.service_type.10029"
        }
    }
}
```

json

火山声音复刻大模型（非流式输入流式输出）
--------------------

该方案支持复刻真人音色，非流式文本输入、流式语音输出，语音合成速度更快，适用于需要定制化音色的文本场景（如企业客服、虚拟主播）。详细功能特性参看[大模型声音复刻](https://www.volcengine.com/docs/6561/133350)。

### 核心参数

> 完整参数及说明可参看 [StartVoiceChat 接口文档](https://www.volcengine.com/docs/6348/1558163)。

| 参数名 | 类型 | 是否必填 | 示例值 | 说明 |
| --- | --- | --- | --- | --- |
| `IgnoreBracketText` | Int[] | 否 | `[1,2]` | 过滤大模型返回内容中指定标点符号中的文字后再进行语音合成。 |
| `Provider` | String | 是 | `volcano` | 语音合成服务提供商，固定取值 `volcano`。 |
| `ProviderParams.app.appid` | String | 是 | `94****11` | 开通火山引擎声音复刻大模型服务后获取的 App ID，用于标识应用。你可登录[豆包语音控制台](https://console.volcengine.com/speech/service/9999?)获取。 |
| `ProviderParams.app.cluster` | String | 是 | `volcano_icl` | 已开通声音复刻大模型服务对应的集群标识（Cluster ID）。你可登录[豆包语音控制台](https://console.volcengine.com/speech/service/9999?)开通服务后获取。 |
| `ProviderParams.audio.voice_type` | String | 是 | `S_N****T7k1` | 声音复刻声音 ID。你可登录[豆包语音控制台](https://console.volcengine.com/speech/service/9999?)获取。 |
| `ProviderParams.audio.speed_ratio` | Float | 否 | `1.0` | 语速。取值范围为`[0.8,2]`，默认值为`1.0`，通常保留一位小数即可。取值越大，语速越快。 |

### 请求示例

你可参看以下示例，使用火山声音复刻大模型（非流式输入流式输出）进行语音合成：

```json
{
    "TTSConfig": {
        "IgnoreBracketText":[
            1,
            2
        ],
        "Provider": "volcano",
        "ProviderParams": {
            "app": {
                "appid": "94****11",
                "cluster": "volcano_icl"
            },
            "audio": {
                "voice_type": "S_N****T7k1",
                "speed_ratio": 1.1
            }
        }
    }
}
```

json

### 注意事项

使用前请参看[声音复刻下单及使用指南](https://www.volcengine.com/docs/6561/1167802)及[声音复刻2.0-最佳实践](https://www.volcengine.com/docs/6561/1204182)了解如何实现声音复刻最佳效果。

火山声音复刻大模型（流式输入流式输出）
-------------------

该方案支持复刻真人音色，流式文本输入、流式语音输出，语音合成效果更自然。详细功能特性参看[大模型声音复刻](https://www.volcengine.com/docs/6561/133350)。

### 核心参数

> 完整参数及说明可参看 [StartVoiceChat 接口文档](https://www.volcengine.com/docs/6348/1558163)。

| 参数名 | 类型 | 是否必填 | 示例值 | 说明 |
| --- | --- | --- | --- | --- |
| `IgnoreBracketText` | Int[] | 否 | `[1,2]` | 过滤大模型返回内容中指定标点符号中的文字后再进行语音合成。 |
| `Provider` | String | 是 | `volcano_bidirection` | 语音合成服务提供商，固定取值 `volcano_bidirection`。 |
| `ProviderParams.app.appid` | String | 是 | `94****11` | 开通火山引擎声音复刻大模型服务后获取的 App ID，用于标识应用。你可登录[豆包语音控制台](https://console.volcengine.com/speech/service/9999?)获取。 |
| `ProviderParams.app.token` | String | 是 | `OaO****ws1` | 与开通声音复刻大模型服务 App ID 对应的 AccessToken，用于身份认证。你可登录[豆包语音控制台](https://console.volcengine.com/speech/service/9999?)获取。Access Token 查找方式，可参看[如何获取 Token](https://www.volcengine.com/docs/6561/196768#q1%EF%BC%9A%E5%93%AA%E9%87%8C%E5%8F%AF%E4%BB%A5%E8%8E%B7%E5%8F%96%E5%88%B0%E4%BB%A5%E4%B8%8B%E5%8F%82%E6%95%B0appid%EF%BC%8Ccluster%EF%BC%8Ctoken%EF%BC%8Cauthorization-type%EF%BC%8Csecret-key-%EF%BC%9F)。 |
| `ProviderParams.audio.voice_type` | String | 是 | `S_N****T7k1` | 声音复刻声音 ID。你可登录[豆包语音控制台](https://console.volcengine.com/speech/service/9999?)获取。 |
| `ProviderParams.audio.speed_ratio` | Float | 否 | `1.0` | 语速。取值范围为`[0.8,2]`，默认值为`1.0`，通常保留一位小数即可。取值越大，语速越快。 |
| `ProviderParams.ResourceId` | String | 是 | `volc.megatts.default` | 调用服务的资源信息 ID，该参数固定取值：`volc.megatts.default`。 |

### 请求示例

你可参看以下示例，使用火山声音复刻大模型（流式输入流式输出）进行语音合成：

```json
{
    "TTSConfig": {
        "IgnoreBracketText":[
            1,
            2
        ],
        "Provider": "volcano_bidirection",
        "ProviderParams": {
            "app": {
                "appid": "94****11",
                "token": "OaO****ws1",
            },
            "audio": {
                "voice_type": "S_N****T7k1",
                "speed_ratio": 1.1
            },
            "ResourceId": "volc.megatts.default"
        }
    }
}
```

json

### 注意事项

使用前请参看[声音复刻下单及使用指南](https://www.volcengine.com/docs/6561/1167802) 及[声音复刻2.0-最佳实践](https://www.volcengine.com/docs/6561/1204182)了解如何实现声音复刻最佳效果。

MiniMax 语音合成
------------

MiniMax 语音合成支持多语言、混合音色和特殊发音标注。详细功能特性参看[MiniMax 语音生成](https://platform.minimaxi.com/document/T2A%20V2?key=66719005a427f0c8a5701643)。

### 核心参数

> 完整参数及说明可参看 [StartVoiceChat 接口文档](https://www.volcengine.com/docs/6348/1558163)。

| 参数名 | 类型 | 是否必填 | 示例值 | 说明 |
| --- | --- | --- | --- | --- |
| `IgnoreBracketText` | Int[] | 否 | `[1,2]` | 过滤大模型返回内容中指定标点符号中的文字后再进行语音合成。 |
| `Provider` | String | 是 | `minimax` | 语音合成服务提供商，固定取值 `minimax`。 |
| `ProviderParams.Authorization` | String | 是 | `eyJhbG****SUzI1N` | API 密钥。前往 [Minimax 账户管理-接口密钥](https://platform.minimaxi.com/login)获取。 |
| `ProviderParams.Groupid` | String | 是 | `983*****669` | 用户所属组 ID。前往 [Minimax 账号信息-基本信息](https://platform.minimaxi.com/login)获取。 |
| `ProviderParams.model` | String | 是 | `speech-01-turbo` | 模型版本（否 `speech-01-turbo`/`speech-01-240228`/`speech-01-turbo-240228`）。 |
| `ProviderParams.URL` | String | 是 | `https://api.minimax.chat/v1/t2a_v2` | 请求语音合成 URL，该参数固定取值：`https://api.minimax.chat/v1/t2a_v2`。 |
| `ProviderParams.stream` | Boolean | 是 | `false` | 是否流式输出。 |
| `ProviderParams.voice_setting` | Object | 否 | `{"speed":1.0,"vol":1.0,"pitch":1.0}` | 语速、音量、音调配置。 |
| `ProviderParams.pronunciation_dict` | Object | 否 | `{"tone": ["处理/(chu3)(li3)", "危险/dangerous"]}` | 特殊发音标注。 |
| `ProviderParams.language_boost` | String | 否 | `Chinese,Yue` | 语言偏好。取值范围：`Chinese`/`Yue`，默认值为 `Chinese`。 |
| `ProviderParams.timber_weights` | Array | 否 | `[{"voice_id":"male-qn-jingying","weight":70},{"voice_id":"wumei_yujie","weight":30}]` | 合成音色权重设置。 |

### 请求示例

你可参看以下示例，使用MiniMax 语音合成进行语音合成：

```json
{
    "TTSConfig": {
        "IgnoreBracketText": [
            1,
            2
        ],
        "Provider": "minimax",
        "ProviderParams": {
            "Authorization": "eyJhbG****SUzI1N",
            "Groupid": "983*****669",
            "model": "speech-01-turbo",
            "URL": "https://api.minimax.chat/v1/t2a_v2",
            "stream": false,
            "voice_setting": {
                "speed": 1.0,
                "vol": 1.0,
                "pitch": 1.0
            },
            "pronunciation_dict": {
                "tone": [
                    "处理/(chu3)(li3)",
                    "危险/dangerous"
                ]
            },
            "language_boost": "Chinese,Yue",
            "timber_weights": [
                {
                    "voice_id": "male-qn-jingying",
                    "weight": 70
                },
                {
                    "voice_id": "wumei_yujie",
                    "weight": 30
                }
            ]
        }
    }
}
```

json

FAQ
---

### 如何更换音色？

针对火山引擎的 TTS 服务，你可以按照以下步骤更换音色：

1.   前往[豆包语音控制台](https://console.volcengine.com/speech)，选择你使用的语音合成服务，如语音合成大模型。
2.   在服务详情页，单击**音色购买**，选择你需要的音色。
3.   购买完成后，在服务详情页查找要更换音色的 `Voice_type`。
4.   调用 `StopVoiceChat` 接口停止当前智能体任务，使用 `StartVoiceChat` 接口，在 `TTSConfig` 中的 `Voice_type` 参数中填入更换音色的 `Voice_type` 重新启动智能体任务。

