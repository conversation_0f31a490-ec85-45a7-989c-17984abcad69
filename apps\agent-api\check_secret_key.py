#!/usr/bin/env python3
"""
检查火山引擎Secret Access Key的格式
"""
import base64
import os
from pathlib import Path
import sys

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from api.settings import settings

def check_secret_key():
    """检查Secret Access Key的格式"""
    print("🔍 检查火山引擎Secret Access Key格式...")
    
    raw_secret = settings.VOLCANO_SECRET_ACCESS_KEY
    print(f"📋 原始Secret Key: {raw_secret}")
    print(f"📋 长度: {len(raw_secret)}")
    
    # 尝试Base64解码
    try:
        decoded = base64.b64decode(raw_secret).decode('utf-8')
        print(f"📋 Base64解码后: {decoded}")
        print(f"📋 解码后长度: {len(decoded)}")
        print("✅ 这个Secret Key确实是Base64编码的！")
        
        # 检查解码后的格式是否像正常的Secret Key
        if len(decoded) >= 20 and all(c.isalnum() for c in decoded):
            print("✅ 解码后的格式看起来像正常的Secret Key")
            return decoded
        else:
            print("❌ 解码后的格式不像正常的Secret Key")
            return raw_secret
            
    except Exception as e:
        print(f"❌ Base64解码失败: {e}")
        print("ℹ️ 这个Secret Key不是Base64编码的")
        return raw_secret

if __name__ == "__main__":
    check_secret_key()
