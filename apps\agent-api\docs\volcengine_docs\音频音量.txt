Title: 音频音量--实时音视频-火山引擎

URL Source: https://www.volcengine.com/docs/6348/81268

Markdown Content:
音频音量--实时音视频-火山引擎


音视频和纯音频通话中，获取通话音量可以实现多种有用的功能，例如：

*   动态展示通话音量条

*   识别当前活跃的发言用户

*   提醒用户是否在发言时忘记开启麦克风

当你希望调整通话音量时，除了通过操作系统进行设置外，也可以通过调用 SDK 接口，分别设置采集、播放或混音音量。

> 混音音量调节详见[使用混音功能](https://www.volcengine.com/docs/6348/70141)。

![Image 6](https://lf3-static.bytednsdoc.com/obj/eden-cn/jvKJ%5BY/ljhwZthlaukjlkulzlp/advancedFeatures/%E9%9F%B3%E9%87%8F.png)

前提条件
----

*   实现了实时音视频通信功能。
*   使用 RTC SDK 内置的音频采集功能。

获取用户音量
------

### 实现方法

你可以通过调用 `enableAudioPropertiesReport` 方法，设置音量回调周期，单位为毫秒，然后在 `onLocalAudioPropertiesReport` 和 `onRemoteAudioPropertiesReport` 回调中分别获取用户及对应的音量信息。

 音量值对应的音量档位详见 [API 文档](https://www.volcengine.com/docs/6348/70083#audiopropertiesinfo)。

### 示例代码

objectivec

java

cpp

```objectivec
// 启用音频信息提示
- (void)enableAudioPropertiesReport {
    ByteRTCAudioPropertiesConfig *audioPropertiesConfig = [[ByteRTCAudioPropertiesConfig alloc] init];
    audioPropertiesConfig.interval = 300;
    [self.rtcVideo enableAudioPropertiesReport:audioPropertiesConfig];
}
/**
  * 回调获取本地麦克风和屏幕音频流采集的音频信息
  * @param engine ByteRTCVideo 对象
  * @param audioPropertiesInfos  本地音频信息，详见 LocalAudioPropertiesInfo
  */
- (void)rtcEngine:(ByteRTCVideo *)engine onLocalAudioPropertiesReport:(NSArray<ByteRTCLocalAudioPropertiesInfo *> *)audioPropertiesInfos {
    // 本地用户音量回调
}

/**
  * 回调获取订阅的远端用户的音频信息
  * @param engine ByteRTCVideo 对象
  *  @param audioPropertiesInfos  远端音频信息，其中包含音频流属性、房间 ID、用户 ID ，详见 ByteRTCRemoteAudioPropertiesInfo
  * @param totalRemoteVolume 订阅的所有远端流混音后的总音量，范围是 [0,255]
  */
- (void)rtcEngine:(ByteRTCVideo *)engine onRemoteAudioPropertiesReport:(NSArray<ByteRTCRemoteAudioPropertiesInfo *> *)audioPropertiesInfos totalRemoteVolume:(NSInteger)totalRemoteVolume {
    // 远端用户音量回调
}
/**
  * 回调获取房间内的最活跃用户信息
  * @param engine ByteRTCVideo 对象
  *  @param  roomId  房间 ID
  *  @param  uid 最活跃用户（ActiveSpeaker）的用户 ID
  */
- (void)rtcEngine:(ByteRTCVideo *)engine onActiveSpeaker:(NSString *)roomId uid:(NSString *)uid {
    // 房间内的最活跃用户信息
}
```

objectivec

```java
// 启用音频信息提示
public void enableAudioPropertiesReport() {
    AudioPropertiesConfig config = new AudioPropertiesConfig(300);
    mRTCVideo.enableAudioPropertiesReport(config);
}

private final IRTCVideoEventHandler mRTCVideoEventHandle = new IRTCVideoEventHandler() {
    /**
      * 回调获取本地麦克风和屏幕音频流采集的音频信息
      * @param audioPropertiesInfos 本地音频信息，详见 LocalAudioPropertiesInfo
      */
    @Override
    public void onLocalAudioPropertiesReport(LocalAudioPropertiesInfo[] audioPropertiesInfos) {
        // 本地用户音量回调
    }

    /**
     * 回调获取订阅的远端用户的音频信息
     * @param audioPropertiesInfos 远端音频信息，其中包含音频流属性、房间 ID、用户 ID ，详见 RemoteAudioPropertiesInfo
     * @param totalRemoteVolume 订阅的所有远端流混音后的总音量
     */
    @Override
    public void onRemoteAudioPropertiesReport(RemoteAudioPropertiesInfo[] audioPropertiesInfos, int totalRemoteVolume) {
        // 远端用户音量回调
    }

    /**
     * 回调获取房间内的最活跃用户信息
     * @param roomId  房间 ID
     * @param uid 最活跃用户（ActiveSpeaker）的用户 ID
     */
    @Override
    public void onActiveSpeaker(String roomId, String uid) {
        // 房间内的最活跃用户信息
    }
    
    ...
  }
```

java

```cpp
// 启用音频信息提示
void RtcEngineWrap::enableAudioPropertiesReport() {
    bytertc::AudioPropertiesConfig config;
    config.interval = 300;
    video_engine_->enableAudioPropertiesReport(config);
}

/**
  * 回调获取本地麦克风和屏幕音频流采集的音频信息
  * @param audio_properties_infos 本地音频信息，详见 [LocalAudioPropertiesInfo](70098#localaudiopropertiesinfo) 
  * @param audio_properties_info_number 数组长度
  */
void RtcEngineWrap::onLocalAudioPropertiesReport(
    const LocalAudioPropertiesInfo * audio_properties_infos,
    int audio_properties_info_number) {
    // 本地用户音量回调
}

/**
 * 回调获取订阅的远端用户的音频信息
 * @param audio_properties_infos 远端音频信息，其中包含音频流属性、房间 ID、用户 ID ，详见 RemoteAudioPropertiesInfo
 * @param audio_properties_info_number 数组长度
 * @param total_remote_volume  所有订阅的远端流的总音量，范围是 [0,255]
 */
void RtcEngineWrap::onRemoteAudioPropertiesReport(
        const bytertc::RemoteAudioPropertiesInfo* audio_properties_infos,
        int audio_properties_info_number, int total_remote_volume) {
    // 远端用户音量回调
}
/**
  * 回调获取房间内的最活跃用户信息
  * @param room_id    房间 ID
  * @param  uid 最活跃用户（ActiveSpeaker）的用户 ID
  */
void RtcEngineWrap::onActiveSpeaker(
    const char* room_id,
    const char* uid) {
    // 房间内的最活跃用户信息
}
```

cpp

### API参考

|  | Android | iOS | macOS | Windows | Linux | Electron | Flutter | Unity | Web |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
| 启用音量提示 | [`enableAudioPropertiesReport`](https://www.volcengine.com/docs/6348/70080#RTCVideo-enableaudiopropertiesreport) | [`enableAudioPropertiesReport:`](https://www.volcengine.com/docs/6348/70086#enableaudiopropertiesreport) | [`enableAudioPropertiesReport:`](https://www.volcengine.com/docs/6348/70092#enableaudiopropertiesreport) | [`enableAudioPropertiesReport`](https://www.volcengine.com/docs/6348/70095#enableaudiopropertiesreport) | [`enableAudioPropertiesReport`](https://www.volcengine.com/docs/6348/85516#enableaudiopropertiesreport) | [`enableAudioPropertiesReport`](https://www.volcengine.com/docs/6348/85532#rtcvideo-enableaudiopropertiesreport) | [`enableAudioPropertiesReport`](https://pub.dev/documentation/volc_engine_rtc/latest/api_bytertc_video_api/RTCVideo/enableAudioPropertiesReport.html) | [`EnableAudioPropertiesReport`](https://www.volcengine.com/docs/6348/191859#IRTCVideo-enableaudiopropertiesreport) | [`enableAudioPropertiesReport()`](https://www.volcengine.com/docs/6348/104478#enableaudiopropertiesreport) |
| 本地音量回调 | [`onLocalAudioPropertiesReport`](https://www.volcengine.com/docs/6348/70081#IRTCVideoEventHandler-onlocalaudiopropertiesreport) | [`rtcEngine:onLocalAudioPropertiesReport:`](https://www.volcengine.com/docs/6348/70087#rtcengine-onlocalaudiopropertiesreport) | [`rtcEngine:onLocalAudioPropertiesReport:`](https://www.volcengine.com/docs/6348/70093#rtcengine-onlocalaudiopropertiesreport) | [`onLocalAudioPropertiesReport`](https://www.volcengine.com/docs/6348/70096#onlocalaudiopropertiesreport) | [`onLocalAudioPropertiesReport`](https://www.volcengine.com/docs/6348/85517#onlocalaudiopropertiesreport) | [`onLocalAudioPropertiesReport`](https://www.volcengine.com/docs/6348/85533#rtcvideocallback-onlocalaudiopropertiesreport) | [`onLocalAudioPropertiesReport`](https://pub.dev/documentation/volc_engine_rtc/latest/api_bytertc_video_event_handler/RTCVideoEventHandler/onLocalAudioPropertiesReport.html) | [`OnLocalAudioPropertiesReportEventHandler`](https://www.volcengine.com/docs/6348/191860#onlocalaudiopropertiesreporteventhandler) | [`onLocalAudioPropertiesReport()`](https://www.volcengine.com/docs/6348/104479#onlocalaudiopropertiesreport) |
| 远端用户音量回调 | [`onRemoteAudioPropertiesReport`](https://www.volcengine.com/docs/6348/70081#IRTCVideoEventHandler-onremoteaudiopropertiesreport) | [`rtcEngine:onRemoteAudioPropertiesReport:totalRemoteVolume:`](https://www.volcengine.com/docs/6348/70087#rtcengine-onremoteaudiopropertiesreport-totalremotevolume) | [`rtcEngine:onRemoteAudioPropertiesReport:totalRemoteVolume:`](https://www.volcengine.com/docs/6348/70093#rtcengine-onremoteaudiopropertiesreport-totalremotevolume) | [`onRemoteAudioPropertiesReport`](https://www.volcengine.com/docs/6348/70096#onremoteaudiopropertiesreport) | [`onRemoteAudioPropertiesReport`](https://www.volcengine.com/docs/6348/85517#onremoteaudiopropertiesreport) | [`onRemoteAudioPropertiesReport`](https://www.volcengine.com/docs/6348/85533#rtcvideocallback-onremoteaudiopropertiesreport) | [`onRemoteAudioPropertiesReport`](https://pub.dev/documentation/volc_engine_rtc/latest/api_bytertc_video_event_handler/RTCVideoEventHandler/onRemoteAudioPropertiesReport.html) | [`OnRemoteAudioPropertiesReportEventHandler`](https://www.volcengine.com/docs/6348/191860#onremoteaudiopropertiesreporteventhandler) | [`onRemoteAudioPropertiesReport()`](https://www.volcengine.com/docs/6348/104479#onremoteaudiopropertiesreport) |
| 活跃用户回调 | [`onActiveSpeaker`](https://www.volcengine.com/docs/6348/70081#onactivespeaker) | [`rtcEngine:onActiveSpeaker:uid:`](https://www.volcengine.com/docs/6348/70087#rtcengine-onactivespeaker-uid) | [`rtcEngine:onActiveSpeaker:uid:`](https://www.volcengine.com/docs/6348/70093#rtcengine-onactivespeaker-uid) | [`onActiveSpeaker`](https://www.volcengine.com/docs/6348/70096#onactivespeaker) | [`onActiveSpeaker`](https://www.volcengine.com/docs/6348/85517#onactivespeaker) | [`onActiveSpeaker`](https://www.volcengine.com/docs/6348/85533#rtcvideocallback-onactivespeaker) | [`onActiveSpeaker`](https://pub.dev/documentation/volc_engine_rtc/latest/api_bytertc_video_event_handler/RTCVideoEventHandler/onActiveSpeaker.html) | 无 | [`onActiveSpeaker()`](https://www.volcengine.com/docs/6348/104479#onactivespeaker) |
| 获取实时音量 | 无 | 无 | 无 | 无 | 无 | 无 | 无 | 无 | [`getAudioVolume`](https://www.volcengine.com/docs/6348/104478#rtcengine-getaudiovolume) |

调整采集音量
------

采集是指音频输入设备，例如麦克风，采集到声音数据并存储到本地的过程。

![Image 7](https://lf3-static.bytednsdoc.com/obj/eden-cn/jvKJ%5BY/ljhwZthlaukjlkulzlp/advancedFeatures/%E9%87%87%E9%9B%86%E9%9F%B3%E9%87%8F.png)

### 实现方法

你可以通过调用 `setCaptureVolume` 方法，实现音量参数调整。

 该方法中`volume`参数表示录音信号的音量，取值范围为 [0,400]：

*   0：静音

*   100：原始音量，不对音频信号幅度进行缩放

*   400: 最大可为原始音量的 4 倍

### 示例代码

objectivec

java

cpp

```objectivec
[self.rtcVideo setCaptureVolume:ByteRTCStreamIndexMain volume:120];
```

objectivec

```java
mRTCVideo.setCaptureVolume(StreamIndex.STREAM_INDEX_MAIN, 120);
```

java

```cpp
video_engine_->setCaptureVolume(StreamIndex::kStreamIndexMain, 120);
```

cpp

### API参考

|  | Android | iOS | macOS | Windows | Linux | Electron | Flutter | Unity | Web |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
| 采集音量 | [`setCaptureVolume`](https://www.volcengine.com/docs/6348/70080#RTCVideo-setcapturevolume) | [`setCaptureVolume:volume:`](https://www.volcengine.com/docs/6348/70086#setcapturevolume-volume) | [`setCaptureVolume:volume:`](https://www.volcengine.com/docs/6348/70092#setcapturevolume-volume) | [`setCaptureVolume`](https://www.volcengine.com/docs/6348/70095#setcapturevolume) | [`setCaptureVolume`](https://www.volcengine.com/docs/6348/85516#setcapturevolume) | [`setCaptureVolume`](https://www.volcengine.com/docs/6348/85532#rtcvideo-setcapturevolume) | [`setCaptureVolume`](https://pub.dev/documentation/volc_engine_rtc/latest/api_bytertc_video_api/RTCVideo/setCaptureVolume.html) | [`SetCaptureVolume`](https://www.volcengine.com/docs/6348/191859#IRTCVideo-setcapturevolume) | [`setCaptureVolume`](https://www.volcengine.com/docs/6348/104478#rtcengine-setcapturevolume) |

调整音量播放
------

播放指客户端接收到远端音视频文件，并通过本地播放设备播放的过程。

![Image 8](https://lf3-static.bytednsdoc.com/obj/eden-cn/jvKJ%5BY/ljhwZthlaukjlkulzlp/advancedFeatures/%E6%92%AD%E6%94%BE%E9%9F%B3%E9%87%8F.png)

### 实现方法

*   所有远端用户音量：你可以通过调用 `setPlaybackVolume` 方法，实现远端所有用户混音后在本地播放的音量。

*   指定远端用户音量：你可以通过调用 `setRemoteAudioPlaybackVolume` 方法，实现指定远端用户在本地播放的音量。

    *   当你需要分别调整多个远端用户在本地播放的音量时，多次调用 `setRemoteAudioPlaybackVolume` 方法，并传入不同的 `uid` 和 `volume` 参数。

    *   当你需要多次调整同一个远端用户在本地播放的音量时，多次调用 `setRemoteAudioPlaybackVolume` 方法，并传入同一个 `uid` 和不同的 `volume` 参数。

### 示例代码

objectivec

java

cpp

```objectivec
int volume = 120;
NSString *uid = @"";
NSString *roomId = @"";
[self.rtcVideo setPlaybackVolume:volume];
[self.rtcVideo setRemoteAudioPlaybackVolume:roomId remoteUid:uid playVolume:volume];
```

objectivec

```java
int volume = 120;
String uid = "";
String roomId = "";
mRTCVideo.setPlaybackVolume(volume);
mRTCVideo.setRemoteAudioPlaybackVolume(roomId, uid, volume);
```

java

```cpp
int volume = 120;
const char *uid = "";
const char *room_id = "";
video_engine_->setPlaybackVolume(volume);
video_engine_->setRemoteAudioPlaybackVolume(room_id, uid, volume);
```

cpp

### API参考

|  | Android | iOS | macOS | Windows | Linux | Electron | Flutter | Unity | Web |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
| 所有远端用户音量 | [`setPlaybackVolume`](https://www.volcengine.com/docs/6348/70080#RTCVideo-setplaybackvolume) | [`setPlaybackVolume:`](https://www.volcengine.com/docs/6348/70086#setplaybackvolume) | [`setPlaybackVolume:`](https://www.volcengine.com/docs/6348/70092#setplaybackvolume) | [`setPlaybackVolume`](https://www.volcengine.com/docs/6348/70095#setplaybackvolume) | [`setPlaybackVolume`](https://www.volcengine.com/docs/6348/85516#setplaybackvolume) | [`setPlaybackVolume`](https://www.volcengine.com/docs/6348/85532#rtcvideo-setplaybackvolume) | [`setPlaybackVolume`](https://pub.dev/documentation/volc_engine_rtc/latest/api_bytertc_video_api/RTCVideo/setPlaybackVolume.html) | [`SetPlaybackVolume`](https://www.volcengine.com/docs/6348/191859#IRTCVideo-setplaybackvolume) | 无 |
| 指定远端用户音量 | [`setRemoteAudioPlaybackVolume`](https://www.volcengine.com/docs/6348/70080#RTCVideo-setremoteaudioplaybackvolume) | [`setRemoteAudioPlaybackVolume:remoteUid:playVolume:`](https://www.volcengine.com/docs/6348/70086#setremoteaudioplaybackvolume-remoteuid-playvolume) | [`setRemoteAudioPlaybackVolume:remoteUid:playVolume:`](https://www.volcengine.com/docs/6348/70092#setremoteaudioplaybackvolume-remoteuid-playvolume) | [`setRemoteAudioPlaybackVolume`](https://www.volcengine.com/docs/6348/70095#setremoteaudioplaybackvolume) | [`setRemoteAudioPlaybackVolume`](https://www.volcengine.com/docs/6348/85516#setremoteaudioplaybackvolume) | [`setRemoteAudioPlaybackVolume`](https://www.volcengine.com/docs/6348/85532#rtcvideo-setremoteaudioplaybackvolume) | [`setRemoteAudioPlaybackVolume`](https://pub.dev/documentation/volc_engine_rtc/latest/api_bytertc_video_api/RTCVideo/setRemoteAudioPlaybackVolume.html) | [`SetRemoteAudioPlaybackVolume`](https://www.volcengine.com/docs/6348/191859#IRTCVideo-setremoteaudioplaybackvolume) | [`setPlaybackVolume`](https://www.volcengine.com/docs/6348/104478#rtcengine-setplaybackvolume) |

FAQ
---

Q: Android 端调用 `enableAudioPropertiesReport` 成功后，收不到本端的音量回调。

A: RTC SDK 内部采用弱引用持有回调实例，需要自行管理回调实例的生命周期。例如：不能用局部内部类形式创建，必须以成员内部类的形式创建。

功能变更日志
------

*   自 Web SDK 4.61 起，你也可以通过 [`getAudioVolume`](https://www.volcengine.com/docs/6348/104478#rtcengine-getaudiovolume) 方法随时获取采集音频音量。
*   自客户端 SDK 3.36 起，本地和远端音量回调支持同时返回线性和非线性音量值。
*   自客户端 SDK 3.29 起，获取音量信息功能可用。

