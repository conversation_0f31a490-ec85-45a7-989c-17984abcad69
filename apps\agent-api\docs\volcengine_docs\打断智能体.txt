Title: 打断智能体--实时音视频-火山引擎

URL Source: https://www.volcengine.com/docs/6348/1511927

Markdown Content:
打断智能体--实时音视频-火山引擎

===============

当 AI 智能体讲话时，如果需要打断智能体发言，并开始新一轮对话，可以通过语音自动打断和手动打断两种方式来实现，以提升对话的流畅度和自然度。

*   **语音自动打断**：系统根据用户的语音输入自动判断并执行打断。支持三种策略：发声即打断、基于用户持续说话时间打断、通过关键词打断。
*   **手动打断**：用户主动发起打断请求，如点击按钮或发送特定命令来打断智能体，可在客户端或服务端实现。

| 应用场景 | 描述 |
| --- | --- |
| 客服对话 | 使用较短的自动打断时长，配合手动打断，实现快速响应和精准控制。 |
| 在线教育 | 采用较长的自动打断时长，避免误打断学生发言，教师可使用手动打断进行必要干预。 |
| 多人会议 | 结合自动打断和手动打断，灵活管理发言顺序和时长。 |

默认打断策略
------

若你未进行任何打断相关配置，或保留相关参数为默认值，系统将采用[发声即打断](https://www.volcengine.com/docs/6348/1511927#default)策略。

语音自动打断
------

自动打断由系统根据用户的音频输入，自动判断是否需要中止智能体的输出。

### 发声即打断

一旦检测到用户发出声音，智能体立刻停止输出。

*   **关键配置**：

 调用 [StartVoiceChat](https://www.volcengine.com/docs/6348/1558163) 时，配置 `Config` 对象下以下参数：

    *   `InterruptMode`：无需设置，保持默认值（`0`）。
    *   `InterruptConfig`：无需设置，保持默认值。
    *   `SilenceTime`：建议保持默认值（`600` ms）或根据具体场景适当调整（例如 800 ms -1200 ms），以确保用户在触发打断后，有足够的时间完整表达其意图，避免因 VAD（语音检测）过早结束而导致语音输入不完整。

*   **请求示例**：

```JSON
{
    "Config": {
        // InterruptMode 保持默认值 0 (发声即打断总开关开启)
        // ASRConfig.InterruptConfig 使用默认值 
        "ASRConfig": {
            // Provider, ProviderParams 等其他 ASR 必要配置需填写
            "VADConfig": {
                "SilenceTime": 600 // 示例：可按需调整，确保用户打断后能完整表达
            }
            // InterruptConfig 及其子参数使用默认值，实现发声即打断
        }
        // 其他 TTSConfig, LLMConfig 等配置也需按实际情况填写
    }
    // 其他请求参数...
}
```
JSON    

### 基于用户持续说话时间打断

当检测到用户持续说话的时长达到预设阈值后，智能体才会停止输出。适用于希望避免因短暂语音（语气词），或环境中无意义持续时间较短的人声（如“好的”）而意外打断智能体的场景。

*   **关键配置**：

 调用 [StartVoiceChat](https://www.volcengine.com/docs/6348/1404673) 时，配置 `Config` 对象下以下参数： 
    *   `InterruptSpeechDuration`：设置为一个合适的毫秒数。该参数取值范围为`[200，3000]`，单位为 ms。
    *   `SilenceTime`：确保此值显著大于 `InterruptSpeechDuration`，以便用户在触发打断后有足够时间说完话。例如，如果 `InterruptSpeechDuration` 是 `300`ms，`SilenceTime` 可以是 `600`ms 或更高。

*   **请求示例**：
```JSON
{
    "Config": {
        "ASRConfig": {
            // Provider, ProviderParams 等其他 ASR 必要配置需填写
            "InterruptConfig": {
                "InterruptSpeechDuration": 300 // 设置用户持续说话300ms后打断
            },
            "VADConfig": {
                "SilenceTime": 600 // 示例，可按需调整，确保大于 InterruptSpeechDuration
            }
        }
        // 其他 TTSConfig, LLMConfig 等配置也需按实际情况填写
    }
    // 其他请求参数...
}
```
JSON    

### 通过关键词打断

当用户说出预设的关键词时，智能体会停止输出。适用于需要用户明确表达意图才能打断的场景，如多人会议。

*   **关键配置**：

 调用 [StartVoiceChat](https://www.volcengine.com/docs/6348/1404673) 时，配置 `Config` 对象下以下参数：

    *   `InterruptKeywords`：配置一个字符串数组，如 `["停止", "下一个"]`。说明

支持同时设置 `InterruptSpeechDuration`，且优先级高于 `InterruptKeywords`。智能体说话时，若用户说话时长未达到 `InterruptSpeechDuration`，即使说话中包含关键词，也不会触发打断。 
    *   通过热词或替换词功能，提高关键词的识别准确率。**仅火山流式语音识别大模型支持**。 
| 功能 | 配置说明 |
| --- | --- |
| 热词 | 如果关键词（如专有名词、特定术语等）识别率较低时，可将其作为热词传入 ASR 模型，提高这些词汇的识别准确度。可通过以下方式传入： * **直接传入**（适用临时或少量关键词场景）： 通过 `ASRConfig.ProviderParams.context`，直接将多个热词作为 JSON 字符串传入。 * **通过热词词表 ID/名称传入**（适用于大量、常用或需要长期维护的热词）： 1. 在火山引擎大模型的自学习平台预先创建热词词表。[如何创建？](https://www.volcengine.com/docs/6561/155739) 2. 调用 [StartVoiceChat](https://www.volcengine.com/docs/6348/1404673) 时，设置参数 `boosting_table_id` 或 `boosting_table_name`。 **优先级**：热词直传优先热词词表。 |
| 替换词 | 将关键词替换为你期望的词汇。可通过步骤传入： 1. 在火山引擎大模型的自学习平台预先创建替换词词表。[如何创建？](https://www.volcengine.com/docs/6561/155739) 2. 调用 [StartVoiceChat](https://www.volcengine.com/docs/6348/1404673) 时，设置参数 `correct_table_id` 或 `correct_table_name`。 **优先级**：替换词优先于热词。即如果一个词同时是热词和替换词的源词，替换词生效。例如，热词里有“苹果”，替换词要求“苹果→Apple”，那最后结果为 “Apple”。 |

*   **请求示例**：

```JSON
{
    "Config": {
        "ASRConfig": {
            "Provider": "volcano", // 使用火山引擎ASR
            "ProviderParams": {
                "Mode": "bigmodel", // 指定使用火山流式语音识别大模型，热词功能（context, boosting_table_id等）仅在 Mode 为 "bigmodel" 时生效
                "AppId": "YOUR_ASR_BIGMODEL_APPID",       // 替换为您的火山大模型ASR AppId
                "AccessToken": "YOUR_ASR_BIGMODEL_TOKEN", // 替换为您的火山大模型ASR AccessToken
                // "ApiResourceId": "volc.bigasr.sauc.duration", // 根据您的购买类型填写
                "context": "{\"hotwords\": [{\"word\":\"停止\"},{\"word\":\"下一个\"},{\"word\":\"我的自定义品牌名\"}]}" // 示例：配置热词直传，提高关键词识别准确率
                // 如果使用热词词表，则配置 boosting_table_id 或 boosting_table_name
                // "boosting_table_id": "YOUR_BOOSTING_TABLE_ID"
            },
            "InterruptConfig": {
                "InterruptKeywords": ["停止", "下一个"] // 设置打断关键词
                // "InterruptSpeechDuration": 500 // 可选：如果同时设置，则时长优先
            },
            "VADConfig": {
                "SilenceTime": 600 //示例：可按需调整，确保用户打断后能完整表达
            }
            // 其他ASRConfig参数，如VolumeGain等，可按需配置
        }
        // 其他 TTSConfig, LLMConfig 等配置也需按实际情况填写
    }
    // 其他请求参数如 AppId, RoomId, TaskId, AgentConfig 等也需填写
}
```
JSON    

### 关闭语音自动打断

如果需要禁用语音自动打断功能，比如希望智能体完整播报信息，而不被用户的语音输入打断，可执行以下操作：

调用 [StartVoiceChat](https://www.volcengine.com/docs/6348/1404673) 时，将 `Config.InterruptMode` 设置 `1`，`InterruptConfig` 保持默认值。

手动打断
----

手动打断为用户主动发起打断请求，通过点击按钮或快捷键方式，主动打断智能体的回答。

 通过服务端或客户端均可实现手动打断操作，你可根据业务请求端种类选择对应的方式。例如你在开发 AI 应用时选择服务端响应请求，则使用服务端实现手动打断，降低请求延迟。

### 通过服务端实现手动打断

调用 [`UpdateVoiceChat`](https://www.volcengine.com/docs/6348/1404671)接口，设置以下参数打断智能体输出：

| 参数 | 类型 | 描述 |
| --- | --- | --- |
| AppId | String | RTC 应用 AppId，参看[获取 AppId](https://www.volcengine.com/docs/6348/69865#%E6%AD%A5%E9%AA%A44%EF%BC%9A%E5%88%9B%E5%BB%BA-rtc-%E5%BA%94%E7%94%A8%EF%BC%8C%E8%8E%B7%E5%8F%96-appid)。 |
| RoomId | String | 替换为调用 StartVoiceChat 时使用的 RoomId。 |
| TaskId | String | 替换为调用 StartVoiceChat 时使用的 TaskId。 |
| Command | String | 填入`interrupt`，表示打断智能体。 |

你可参看以下示例从服务端实现打断操作：

```json
POST https://rtc.volcengineapi.com?Action=UpdateVoiceChat&Version=2024-12-01
{
    "AppId": "661e****543cf", 
    "RoomId": "Room1", 
    "TaskId": "task1", 
    "Command": "interrupt"
}
```

json

### 通过客户端实现手动打断

使用 [`SendUserBinaryMessage`](https://www.volcengine.com/docs/6348/70080#RTCRoom-senduserbinarymessage) 接口实现打断操作。该接口的 `buffer` 参数需要传入特定格式的内容，下图展示了 `buffer` 参数的格式：

![Image 4: alt](https://portal.volccdn.com/obj/volcfe/cloud-universal-doc/upload_d5f49c0ca26d16c6e300267b8fd76ef6.jpg)

| 参数名 | 类型 | 描述 |
| --- | --- | --- |
| magic_number | binary | 消息格式标识符，当前场景消息格式固定为 `ctrl`，用于标识该消息为控制消息。 |
| length | binary | 打断消息长度，单位为字节，采用大端序（Big-endian）存储方式，用于说明 `control_message` 字段的字节长度。 |
| control_message | binary | 打断行为配置信息，采用 JSON 格式，具体内容格式参看 [control_message 格式](https://www.volcengine.com/docs/6348/1511927#control_message)。 |

control_message:

| 参数名 | 类型 | 描述 |
| --- | --- | --- |
| Command | String | 控制命令，此处填入 `interrupt`，表示打断智能体。 |

你可参看以下示例从客户端实现打断操作：

C++

Java

TypeScript

```C++
// 发送打断指令
void sendInterruptMessage(const std::string &uid) {
    nlohmann::json json_data;
    json_data["Command"] = "interrupt";
    sendUserBinaryMessage(uid, json_data.dump());
}
void buildBinaryMessage(const std::string& magic_number, const std::string& message, size_t& binary_message_length, std::shared_ptr<uint8_t[]>& binary_message) { //将字符串包装成 TLV
    auto magic_number_length = magic_number.size();
    auto message_length = message.size();

    binary_message_length = magic_number_length + 4 + message_length;
    binary_message = std::shared_ptr<uint8_t[]>(new uint8_t[binary_message_length]);
    std::memcpy(binary_message.get(), magic_number.data(), magic_number_length);
    binary_message[magic_number_length] = static_cast<uint8_t>((message_length >> 24) & 0xFF);
    binary_message[magic_number_length+1] = static_cast<uint8_t>((message_length >> 16) & 0xFF);
    binary_message[magic_number_length+2] = static_cast<uint8_t>((message_length >> 8) & 0xFF);
    binary_message[magic_number_length+3] = static_cast<uint8_t>(message_length & 0xFF);
    std::memcpy(binary_message.get()+magic_number_length+4, message.data(), message_length);
}

int sendUserBinaryMessage(const std::string &uid, const std::string& message) {
    if (rtcRoom_ != nullptr)
    {
        size_t length = 0;
        std::shared_ptr<uint8_t[]> binary_message = nullptr;
        buildBinaryMessage("ctrl", message, length, binary_message);
        return rtcRoom_->sendUserBinaryMessage(uid.c_str(), static_cast<int>(length), binary_message.get());
    }
    return -1;
```

C++

```Java
// 发送打断指令
public void sendInterruptMessage(String userId) {
    JSONObject json = new JSONObject();
    try {
        json.put("Command", "interrupt");
    } catch (JSONException e) {
        throw new RuntimeException(e);
    }
    String jsonString = json.toString();
    byte[] buildBinary = buildBinaryMessage("ctrl", jsonString);
    sendUserBinaryMessage(userId, buildBinary);
}
private byte[] buildBinaryMessage(String magic_number, String content) { //将字符串包装成 TLV
    byte[] prefixBytes = magic_number.getBytes(StandardCharsets.UTF_8);
    byte[] contentBytes = content.getBytes(StandardCharsets.UTF_8);
    int contentLength = contentBytes.length;

    ByteBuffer buffer = ByteBuffer.allocate(prefixBytes.length + 4 + contentLength);
    buffer.order(ByteOrder.BIG_ENDIAN);
    buffer.put(prefixBytes);
    buffer.putInt(contentLength);
    buffer.put(contentBytes);
    return buffer.array();
}

public void sendUserBinaryMessage(String userId, byte[] buffer) {
    if (rtcRoom_ != null) {
        rtcRoom_.sendUserBinaryMessage(userId, buffer, MessageConfig.RELIABLE_ORDERED);
    }
}
```

Java

```TypeScript
import VERTC from '@volcengine/rtc';

/**
 * @brief 智能体配置
 */
const BotName = 'RobotMan_'; // 自定义智能体名称
const CommandKey = 'ctrl';
const engine = VERTC.createEngine('Your AppID'); // RTC 应用 AppId

/**
 * @brief 指令类型
 */
enum COMMAND {
  /**
   * @brief 打断指令
   */
  INTERRUPT = 'interrupt',
};

/**
 * @brief 将字符串包装成 TLV
 */
function stringToTLV(inputString: string, type = '') {
  const typeBuffer = new Uint8Array(4);

  for (let i = 0; i < type.length; i++) {
    typeBuffer[i] = type.charCodeAt(i);
  }

  const lengthBuffer = new Uint32Array(1);
  const valueBuffer = new TextEncoder().encode(inputString);

  lengthBuffer[0] = valueBuffer.length;

  const tlvBuffer = new Uint8Array(typeBuffer.length + 4 + valueBuffer.length);

  tlvBuffer.set(typeBuffer, 0);

  tlvBuffer[4] = (lengthBuffer[0] >> 24) & 0xff;
  tlvBuffer[5] = (lengthBuffer[0] >> 16) & 0xff;
  tlvBuffer[6] = (lengthBuffer[0] >> 8) & 0xff;
  tlvBuffer[7] = lengthBuffer[0] & 0xff;

  tlvBuffer.set(valueBuffer, 8);

  return tlvBuffer.buffer;
};

/**
 * @brief 发送打断指令
 */
engine.sendUserBinaryMessage(
  BotName,
  stringToTLV(
    JSON.stringify({
      Command: COMMAND.INTERRUPT,
    }),
    CommandKey,
  )
);
```

TypeScript

接收打断通知
------

通过状态回调功能，实时获取 AI 智能体在对话过程中被打断的通知，支持通过业务服务器或客户端接收打断通知。

具体实现操作，请参见[接收状态变化消息](https://www.volcengine.com/docs/6348/1415216)。

FAQ
---

### 如何设置打断灵敏度，来避免背景噪音或自己声音打断智能体？

可以尝试以下方法来降低背景噪声干扰：

*   **方法 1：使用关键词打断**

 只有在检测到指定关键词时才会打断智能体。例如，在会议预约场景中，可设置“取消”/“停止”等关键词，只有当用户说出这些词时，才会打断智能体，有效避免了背景噪音随机触发打断的情况。[如何设置关键词打断？](https://www.volcengine.com/docs/6348/1511927#keywords)

*   **方法 2：降低打断阈值组合策略**

    *   **设置打断持续时长**（`InterruptSpeechDuration`）：设置为一个大于 0 的值（例如 2000 ms），只有当声音持续达到该时长后才会触发打断，有效过滤短暂噪音。
    *   **调整音量增益**（`VolumeGain`）：适当降低 VolumeGain 的值，从而降低送入 ASR 的整体音量，使得较小的背景音可能不被识别为语音活动。需要注意，过度降低可能影响正常语音识别。

**请求示例如下**：

```json
POST https://rtc.volcengineapi.com?Action=StartVoiceChat&Version=2024-12-01
{
   "Config": {
       "ASRConfig": {
           // Provider, ProviderParams 等其他 ASR 必要配置需填写
           "InterruptConfig": {
               "InterruptSpeechDuration": 2000 // 设置用户持续说话2000ms后打断
           },
           "VADConfig": {
               "SilenceTime": 600 // 示例，可按需调整，确保大于 InterruptSpeechDuration
           }
           "VolumeGain": 0.3 
       }
       // 其他 TTSConfig, LLMConfig 等配置也需按实际情况填写
   }
   // 其他请求参数...
}
```

json

*   **方法 3：关闭语音自动打断，采用手动打断**

 如果场景允许，可关闭语音自动打断功能，让用户通过手动操作（如按键）来打断智能体。这样用户可以根据自己的需求和判断，在合适的时机主动发起打断，完全避免了背景噪音自动触发打断的可能性。具体操作，请参看[关闭语音自动打断](https://www.volcengine.com/docs/6348/1511927#%E5%85%B3%E9%97%AD%E8%AF%AD%E9%9F%B3%E8%87%AA%E5%8A%A8%E6%89%93%E6%96%AD)和[手动打断](https://www.volcengine.com/docs/6348/1511927#%E6%89%8B%E5%8A%A8%E6%89%93%E6%96%AD)。

### VAD 对语音打断持续时长有影响吗？

VAD 配置（`SilenceTime`）不直接改变 `InterruptSpeechDuration` 的阈值或行为逻辑。但是，一个设置得过短的 `SilenceTime` 会导致用户的语音输入被过早地判定为结束。如果用户试图通过持续说话（即依赖 `InterruptSpeechDuration`> 0）来打断 AI，而他的话还没说到满足 `InterruptSpeechDuration` 的时长就被过短的 `SilenceTime` 切断了，那么基于时长的打断就不会发生。用户会感觉“我说了一半，系统就认为我说完了，也没打断 AI”。

### 如智能体说话时被打断，已经输出的内容会存入上下文吗？

不会存入上下文。

